#!/bin/bash

echo "Building JermesaCode Ai Android App..."
echo

echo "Cleaning previous builds..."
./gradlew clean

echo "Building debug APK..."
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo
    echo "✓ Build successful!"
    echo "APK location: app/build/outputs/apk/debug/app-debug.apk"
    echo
    echo "To install on device, run:"
    echo "./gradlew installDebug"
else
    echo
    echo "✗ Build failed!"
    echo "Check the error messages above."
fi
