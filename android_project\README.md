# JermesaCode Ai - Android WebView Application

A native Android application that wraps the JermesaCode Ai web interface in a WebView, providing a native mobile experience for the AI chat application.

## Features

- **Native Android WebView**: Loads the web application from local assets
- **CORS-Free API Access**: JavaScript bridge bypasses CORS restrictions for AI provider APIs
- **File Upload Support**: Camera capture and file picker integration
- **Download Handling**: Automatic download management for chat history exports
- **Streaming Support**: Real-time AI responses with proper streaming handling
- **Permissions Management**: Camera, storage, and internet access
- **Modern Android Support**: Targets API 34 with backward compatibility to API 24
- **Splash Screen**: Modern splash screen implementation
- **Back Button Handling**: Proper WebView navigation

## Project Structure

```
android_project/
├── app/
│   ├── build.gradle                 # App-level build configuration
│   ├── proguard-rules.pro          # ProGuard rules
│   └── src/main/
│       ├── AndroidManifest.xml     # App manifest with permissions
│       ├── java/com/jermescode/ai/
│       │   └── MainActivity.java   # Main activity with WebView
│       ├── res/
│       │   ├── layout/
│       │   │   └── activity_main.xml
│       │   ├── values/
│       │   │   ├── colors.xml
│       │   │   ├── strings.xml
│       │   │   └── themes.xml
│       │   ├── xml/
│       │   │   ├── backup_rules.xml
│       │   │   ├── data_extraction_rules.xml
│       │   │   └── file_paths.xml
│       │   └── mipmap-*/            # App icons
│       └── assets/www/              # Web application files
├── gradle/wrapper/                  # Gradle wrapper files
├── build.gradle                     # Project-level build configuration
├── gradle.properties               # Gradle properties
├── gradlew                         # Gradle wrapper script (Unix)
├── gradlew.bat                     # Gradle wrapper script (Windows)
├── settings.gradle                 # Project settings
└── README.md                       # This file
```

## Requirements

- **Android Studio**: Arctic Fox (2020.3.1) or later
- **Android SDK**: API 24 (Android 7.0) or higher
- **Java**: JDK 8 or higher
- **Gradle**: 8.0 (included via wrapper)

## Building the Project

### Option 1: Using Android Studio (Recommended)

1. **Open Android Studio**
2. **Import Project**: 
   - Click "Open an existing Android Studio project"
   - Navigate to the `android_project` folder
   - Click "OK"
3. **Sync Project**: 
   - Android Studio will automatically sync the project
   - Wait for the sync to complete
4. **Build**: 
   - Click "Build" → "Make Project" or press `Ctrl+F9`
5. **Run**: 
   - Connect an Android device or start an emulator
   - Click "Run" → "Run 'app'" or press `Shift+F10`

### Option 2: Using Command Line

1. **Navigate to project directory**:
   ```bash
   cd android_project
   ```

2. **Build the project**:
   ```bash
   # On Windows
   gradlew.bat assembleDebug
   
   # On macOS/Linux
   ./gradlew assembleDebug
   ```

3. **Install on device**:
   ```bash
   # On Windows
   gradlew.bat installDebug
   
   # On macOS/Linux
   ./gradlew installDebug
   ```

## Configuration

### App Information
- **Package Name**: `com.jermescode.ai`
- **App Name**: JermesaCode Ai
- **Version**: 1.0 (Version Code: 1)
- **Target SDK**: 34 (Android 14)
- **Minimum SDK**: 24 (Android 7.0)

### Permissions
The app requests the following permissions:
- `INTERNET` - For AI API calls
- `ACCESS_NETWORK_STATE` - Network connectivity checks
- `CAMERA` - Photo capture functionality
- `READ_EXTERNAL_STORAGE` - File access (API < 33)
- `WRITE_EXTERNAL_STORAGE` - File writing (API < 29)
- `READ_MEDIA_IMAGES` - Image access (API 33+)
- `READ_MEDIA_VIDEO` - Video access (API 33+)
- `READ_MEDIA_AUDIO` - Audio access (API 33+)

## Troubleshooting

### Common Issues

1. **Build Fails with SDK Error**:
   - Ensure Android SDK is properly installed
   - Check that `ANDROID_HOME` environment variable is set
   - Update SDK tools in Android Studio

2. **WebView Not Loading**:
   - Check that all web files are in `assets/www/`
   - Verify internet permission is granted
   - Check device logs with `adb logcat`

3. **AI API Calls Not Working** (FIXED):
   - ✅ **Solution Implemented**: Android bridge bypasses CORS restrictions
   - The app now routes API calls through native Android code
   - Check internet connection and API keys
   - Verify the provider URL is accessible

4. **File Upload Not Working**:
   - Ensure camera and storage permissions are granted
   - Test on a physical device (emulator may have limitations)

5. **App Crashes on Startup**:
   - Check minimum SDK version compatibility
   - Verify all required dependencies are included

6. **Streaming Responses Not Working** (FIXED):
   - ✅ **Solution Implemented**: Custom streaming handler for Android
   - Real-time AI responses now work properly
   - Check Android logs for streaming debug information

### Debug Commands

```bash
# View device logs
adb logcat | grep "JermesaCode"

# Install debug APK manually
adb install app/build/outputs/apk/debug/app-debug.apk

# Clear app data
adb shell pm clear com.jermescode.ai
```

## Development Notes

- The WebView loads content from `file:///android_asset/www/index.html`
- JavaScript is enabled with DOM storage support
- File access is configured for local asset loading
- The app handles external URLs by opening them in the default browser
- Download functionality saves files to the device's Downloads folder

## License

This project follows the same license as the original JermesaCode Ai web application.
