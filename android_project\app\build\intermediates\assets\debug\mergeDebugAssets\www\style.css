/* Reference Image Design Variables */
:root {
    /* Common variables */
    --transition: all 0.2s ease;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
}

/* Light Theme */
.light-theme {
    --primary-color: #0ea5e9;
    --primary-hover: #0284c7;
    --secondary-color: #6366f1;
    --accent-color: #8b5cf6;
    --background-color: #ffffff;
    --sidebar-bg: #ffffff;
    --chat-bg: #ffffff;
    --message-user-bg: #81ccff; /* Very light blue */
    --message-user-icon-bg: #0ea5e9; /* Blue background for icon */
    --message-assistant-bg: #f1f5f9;
    --text-color: #0f172a;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --input-bg: #ffffff;
    --input-border: #e2e8f0;
    --input-focus-border: #0ea5e9;
    --input-focus-shadow: rgba(14, 165, 233, 0.2);
    --scrollbar-track: #f1f5f9;
    --scrollbar-thumb: #cbd5e1;
    --code-bg: #f1f5f9;
    --code-border: #e2e8f0;
    --file-preview-bg: #f8fafc;
    --file-content-bg: #f1f5f9;
    --editor-bg: #ffffff;
    --editor-header-bg: #f1f5f9;
    --editor-output-bg: #f8fafc;
    --editor-output-text: #0f172a;
    --editor-button-bg: #e0f2fe;
    --editor-button-hover: #bae6fd;
}

/* Fix for white logo in light mode */
.light-theme .logo-icon {
    filter: invert(1);
}

/* Dark Theme */
.dark-theme {
    --primary-color: #0ea5e9;
    --primary-hover: #38bdf8;
    --secondary-color: #818cf8;
    --accent-color: #a78bfa;
    --background-color: #2C2C2E;
    --sidebar-bg: #1C1C1E;
    --chat-bg: #2C2C2E;
    --message-user-bg: #459fff;
    --message-user-icon-bg: #007AFF;
    --message-assistant-bg: #3A3A3C;
    --text-color: #FFFFFF;
    --text-secondary: #8E8E93;
    --text-tertiary: #48484A;
    --border-color: #48484A;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --input-bg: #3A3A3C;
    --input-border: #48484A;
    --input-focus-border: #007AFF;
    --input-focus-shadow: rgba(0, 122, 255, 0.3);
    --scrollbar-track: #2C2C2E;
    --scrollbar-thumb: #48484A;
    --code-bg: #383838;
    --code-border: #444444;
    --file-preview-bg: #333333;
    --file-content-bg: #4b4b4b;
    --editor-bg: #272727;
    --editor-header-bg: #2d2d2e;
    --editor-output-bg: #2c2c2c;
    --editor-output-text: #f8fafc;
    --editor-button-bg: #525353;
    --editor-button-hover: #475569;
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-sans);
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
    height: 100vh;
    overflow: hidden;
    transition: var(--transition);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Default to dark theme to match reference image */
body.dark-theme {
    --primary-color: #007AFF;
    --primary-hover: #0056CC;
    --secondary-color: #5856D6;
    --accent-color: #34C759;
    --background-color: #2C2C2E;
    --sidebar-bg: #1C1C1E;
    --chat-bg: #2C2C2E;
    --message-user-bg: #007AFF;
    --message-user-icon-bg: #007AFF;
    --message-assistant-bg: #3A3A3C;
    --text-color: #FFFFFF;
    --text-secondary: #8E8E93;
    --text-tertiary: #48484A;
    --border-color: #48484A;
    --input-bg: #3A3A3C;
    --input-border: #48484A;
    --input-focus-border: #007AFF;
    --input-focus-shadow: rgba(0, 122, 255, 0.3);
    --scrollbar-track: #2C2C2E;
    --scrollbar-thumb: #48484A;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Layout */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    z-index: 10;
    padding: 16px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0 16px 0;
    margin-bottom: 16px;
}

.app-logo {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    color: var(--text-color);
}

.logo-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: 1.1rem;
    padding: var(--spacing-xs);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.theme-toggle:hover {
    color: var(--primary-color);
    background-color: var(--background-color);
}

.new-chat-button {
    margin: 0 0 24px 0;
    padding: 12px 16px;
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: var(--transition);
    font-size: 14px;
    width: 100%;
}

.new-chat-button:hover {
    background-color: var(--input-bg);
    color: var(--text-color);
}

.history-section {
    flex: 1;
}

.history-header {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-action-button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-action-button:hover {
    color: var(--primary-color);
    background-color: var(--input-bg);
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    max-height: calc(100vh - 200px);

}

.chat-item {
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    margin-bottom: 4px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--text-secondary);
    font-size: 14px;
}

.chat-item span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item:hover {
    background-color: var(--background-color);
}

.chat-item.active {
    background-color: var(--primary-color);
    color: white;
}

.chat-item.active img {
    filter: brightness(0) invert(1) !important;
}

.delete-chat {
    background: none;
    border: none;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    padding: var(--spacing-xs);
    margin-left: var(--spacing-sm);
    color: inherit;
}

.delete-chat:hover {
    opacity: 1;
    color: #ef4444;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--background-color);
}

.chat-window {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--chat-bg);
    max-width: 1500px;
    width: 100%;
    margin: 0 auto;
    height: 100%;
    overflow: hidden;
    transition: var(--transition);
}

/* Chat Header */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 16px 24px;
    background-color: var(--chat-bg);
    border-bottom: 1px solid var(--border-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.icon-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-button:hover {
    background-color: var(--input-bg);
}

.icon {
    width: 20px;
    height: 20px;
    filter: brightness(0) saturate(100%) invert(56%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(95%) contrast(86%);
}

.icon-button:hover .icon {
    filter: brightness(0) invert(1);
}

/* Font Awesome icons in header buttons */
.icon-button i {
    color: var(--text-secondary);
    font-size: 16px;
    transition: var(--transition);
}

.icon-button:hover i {
    color: var(--text-color);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.mobile-sidebar-toggle:hover {
    background-color: var(--input-bg);
}

/* Main Content Layout */
.main-content {
    flex: 1;
    display: flex;
    height: 100%;
    overflow: hidden;
    position: relative;
}

/* Chat-Editor Resize Handle */
.chat-editor-resize {
    width: 6px;
    background-color: var(--border-color);
    cursor: col-resize;
    position: relative;
    z-index: 10;
    display: none;
}

.chat-editor-resize:hover,
.chat-editor-resize.active {
    background-color: var(--primary-color);
}

.chat-editor-resize::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 30px;
    width: 2px;
    background-color: var(--text-tertiary);
}

/* Chat Display */
.chat-display {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: var(--chat-bg);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.chat-messages {
    flex: 1;
    padding: 24px; /* Reduce padding */
    display: flex;
    flex-direction: column;
    gap: 24px;
    scroll-behavior: smooth;
    overflow-y: auto;
}

/* Code Editor Panel */
.code-editor-panel {
    width: 100%;
    min-width: 400px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--editor-bg);
    border-left: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    z-index: 100; /* Ensure it's above other elements */
}

/* Editor header with controls */
.code-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--editor-header-bg);
    border-bottom: 1px solid var(--border-color);
}

.code-editor-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.editor-tab-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.language-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 0.9rem;
}

.run-code-button,
.share-to-chat-button,
.fullscreen-preview-button,
.copy-code-button,
.download-code-button,
.editor-tab-button {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: none;
    background-color: var(--editor-button-bg);
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition);
}

.run-code-button:hover,
.share-to-chat-button:hover,
.fullscreen-preview-button:hover,
.copy-code-button:hover,
.download-code-button:hover,
.editor-tab-button:hover {
    background-color: var(--editor-button-hover);
}

/* Success feedback for copy button */
.copy-code-button.success {
    background-color: #10b981; /* Green color */
    color: white;
}

.editor-tab-button.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.close-editor-button {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    margin-left: var(--spacing-sm);
}

.close-editor-button:hover {
    color: var(--text-color);
    background-color: var(--editor-button-bg);
}

/* Editor content area */
.editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.editor-container {
    flex: 1;
    min-height: 200px;
    position: relative;
}

/* CodeMirror customization */
.CodeMirror {
    height: 100% !important;
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.6;
}

/* Resize handle */
.resize-handle {
    height: 6px;
    background-color: var(--border-color);
    cursor: row-resize;
    position: relative;
}

.resize-handle:hover,
.resize-handle.active {
    background-color: var(--primary-color);
}

.resize-handle::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 2px;
    background-color: var(--text-tertiary);
}

/* Preview container */
.preview-container {
    height: 100%;
    min-height: 150px;
    max-height: 60%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Code output area */
.code-output {
    flex: 1;
    background-color: var(--editor-output-bg);
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
    color: var(--text-secondary);
}

.output-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.clear-output-button {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: 0.9rem;
    padding: var(--spacing-xs);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.clear-output-button:hover {
    color: var(--text-color);
    background-color: var(--editor-button-bg);
}

.output-content-wrapper {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100%;
}

#output-content {
    margin: 0;
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--editor-output-text);
    white-space: pre-wrap;
    word-break: break-word;
    min-height: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    width: 100%;
    padding-bottom: 0;
}

/* Layout variations */
.code-editor-panel.vertical-layout .editor-content {
    flex-direction: column;
}

.code-editor-panel.horizontal-layout .editor-content {
    flex-direction: row !important;
    height: 100% !important;
    display: flex !important;
    width: 100% !important;
    overflow: hidden !important;
    position: relative !important;
}

.code-editor-panel.horizontal-layout .resize-handle {
    width: 6px !important;
    height: 100% !important;
    min-height: 100% !important;
    cursor: col-resize !important;
    background-color: var(--border-color);
    position: relative;
    display: block !important;
    z-index: 10 !important;
    flex: 0 0 auto !important;
}

.code-editor-panel.horizontal-layout .resize-handle::before {
    width: 2px !important;
    height: 30px !important;
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--text-tertiary);
}

.code-editor-panel.horizontal-layout .preview-container {
    height: 100% !important;
    width: 100% !important;
    max-height: none !important;
    max-width: 100% !important;
    flex: 0 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: auto !important;
    border-left: 1px solid var(--border-color);
    position: relative !important;
    visibility: visible !important;
    z-index: 1 !important;
    min-width: 200px !important;
}

.code-editor-panel.horizontal-layout .editor-container {
    height: 100% !important;
    width: 60% !important;
    min-width: 200px !important;
    flex: 0 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: auto !important;
    position: relative !important;
    visibility: visible !important;
    z-index: 1 !important;
}

.code-editor-panel.horizontal-layout .CodeMirror {
    flex: 1;
    height: auto !important;
}

/* Ensure code and preview modes work properly in horizontal layout */
.code-editor-panel.horizontal-layout.code-mode .editor-container {
    width: 100% !important;
    display: block !important;
}

.code-editor-panel.horizontal-layout.code-mode .preview-container,
.code-editor-panel.horizontal-layout.code-mode .resize-handle {
    display: none !important;
}

.code-editor-panel.horizontal-layout.preview-mode .preview-container {
    width: 100% !important;
    display: block !important;
}

.code-editor-panel.horizontal-layout.preview-mode .editor-container,
.code-editor-panel.horizontal-layout.preview-mode .resize-handle {
    display: none !important;
}

/* Preview-only mode */
.code-editor-panel.preview-mode .editor-container {
    display: none !important;
}

.code-editor-panel.preview-mode .resize-handle {
    display: none !important;
}

.code-editor-panel.preview-mode .preview-container {
    height: 100% !important;
    max-height: none !important;
    display: block !important;
}

/* Code-only mode */
.code-editor-panel.code-mode .preview-container {
    display: none !important;
}

.code-editor-panel.code-mode .resize-handle {
    display: none !important;
}

.code-editor-panel.code-mode .editor-container {
    height: 100% !important;
    display: block !important;
}

/* Fullscreen preview mode - SIMPLIFIED VERSION */
.code-editor-panel.fullscreen-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100%;
    z-index: 1000;
    background-color: var(--editor-bg);
}

/* Basic fullscreen layout */
.code-editor-panel.fullscreen-preview .editor-content {
    height: calc(100% - 60px) !important; /* Account for header */
    overflow: hidden !important;
    display: flex !important;
}

/* Force horizontal layout in fullscreen */
.code-editor-panel.fullscreen-preview .editor-content {
    flex-direction: row !important;
}

/* Editor container in fullscreen */
.code-editor-panel.fullscreen-preview .editor-container {
    height: 100% !important;
    width: 50% !important;
    display: flex !important;
    visibility: visible !important;
    overflow: auto !important;
    flex: 1 !important;
}

/* Preview container in fullscreen */
.code-editor-panel.fullscreen-preview .preview-container {
    height: 100% !important;
    width: 50% !important;
    display: flex !important;
    visibility: visible !important;
    overflow: auto !important;
    flex: 1 !important;
    border-left: 1px solid var(--border-color);
}

/* Make sure the iframe takes full height in fullscreen */
.code-editor-panel.fullscreen-preview #preview-frame {
    height: 100% !important;
    min-height: 100% !important;
    flex: 1 !important;
}

/* Make sure the output content takes full height in fullscreen */
.code-editor-panel.fullscreen-preview .output-content-wrapper,
.code-editor-panel.fullscreen-preview #output-content {
    height: 100% !important;
    min-height: 100% !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Resize handle in fullscreen */
.code-editor-panel.fullscreen-preview .resize-handle {
    width: 10px !important;
    height: 100% !important;
    cursor: col-resize !important;
    display: block !important;
    background-color: var(--border-color);
    position: absolute !important;
    top: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 100 !important;
    opacity: 0.5 !important;
}

.code-editor-panel.fullscreen-preview .resize-handle:hover,
.code-editor-panel.fullscreen-preview .resize-handle.active {
    opacity: 0.8 !important;
    background-color: var(--accent-color) !important;
}

.code-editor-panel.fullscreen-preview .resize-handle::before {
    width: 2px !important;
    height: 30px !important;
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--text-tertiary);
}

/* Change icon for fullscreen button */
.code-editor-panel.fullscreen-preview .fullscreen-preview-button i::before {
    content: '\f066'; /* fa-compress */
}

/* Hide chat-editor resize handle in fullscreen mode */
.code-editor-panel.fullscreen-preview + .chat-editor-resize {
    display: none !important;
}

/* Override code and preview modes in fullscreen to always show both panels */
.code-editor-panel.fullscreen-preview.code-mode .editor-container,
.code-editor-panel.fullscreen-preview.preview-mode .editor-container,
.code-editor-panel.fullscreen-preview.code-mode .preview-container,
.code-editor-panel.fullscreen-preview.preview-mode .preview-container {
    display: flex !important;
}

.code-editor-panel.fullscreen-preview.code-mode .resize-handle,
.code-editor-panel.fullscreen-preview.preview-mode .resize-handle {
    display: block !important;
}

/* Hide tab buttons in fullscreen mode */
.code-editor-panel.fullscreen-preview .editor-tab-controls {
    display: none !important;
}

/* Ensure CodeMirror takes full height in fullscreen */
.code-editor-panel .CodeMirror {
    height: 100% !important;
    flex: 1 !important;
}

/* Preview styles */
.code-editor-panel .preview-container #code-output {
    height: 100%;
    width: 100%;
}

.code-editor-panel .preview-container #code-output-content-wrapper {
    height: 100%;
    width: 100%;
}

.code-editor-panel .preview-container #output-content {
    height: 100%;
    width: 100%;
    overflow: hidden;
    padding: 0;
}
.preview-header {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--editor-header-bg);
    color: var(--text-color);
    font-weight: 500;
    font-family: var(--font-sans);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.css-preview-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: white;
}

#preview-frame {
    flex: 1;
    background-color: white;
    width: 100%;
    height: 100%;
    min-height: 100%;
    display: block;
}

.message-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    max-width: 80%;
    position: relative;
    animation: fadeIn 0.3s ease-out;
    margin-bottom: 24px;
    width: 100%;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message {
    flex: 1;
    color: #FFFFFF; /* Set text color to white */
    line-height: 1.2; /* Reduce line spacing */
    padding: 16px 20px;
    border-radius: var(--border-radius-lg);
    line-height: 1.5;
    font-size: 15px;
    position: relative;
    word-break: break-word;
}

/* Removed ::before pseudo-element styling - using actual avatar elements now */

.user-message {
    align-self: flex-end;
    margin-left: auto;
    background-color: var(--message-user-bg);
    color: white;
    font-weight: 500;
}

.user-message-container {
    align-self: flex-end;
    margin-left: auto;
    flex-direction: row-reverse;
    justify-content: flex-end;
    max-width: 80%;
}

.user-avatar {
    background-color: var(--message-user-icon-bg);
    margin-left: 12px;
    margin-right: 0;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-avatar img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}

/* Specific styling for dark theme user messages */
.dark-theme .user-message {
    color: #f8fafc; /* White text for dark theme */
    border: 1px solid rgba(255, 255, 255, 0.15);
}



.assistant-message {
    align-self: flex-start;
    background-color: var(--message-assistant-bg);
    color: var(--text-color);
    font-weight: 400;
    padding-top: 40px;
}

/* Removed ::before pseudo-element styling for assistant messages - using actual avatar elements now */

.system-message {
    align-self: center;
    background-color: transparent;
    color: var(--text-color);
    font-style: italic;
    padding: var(--spacing-sm) var(--spacing-xl);
    max-width: 80%;
    text-align: center;
    font-size: 0.9rem;
    line-height: 1.2;
}

.copy-button {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: #ffffff;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    opacity: 0;
    transition: opacity 0.2s;
    color: var(--text-tertiary);
    padding: var(--spacing-xs);
    border-radius: 50%;
    z-index: 2;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-message .copy-button {
    color: var(--text-secondary);
    background: rgba(0, 0, 0, 0.05);
}

.dark-theme .user-message .copy-button {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
}

.message-container:hover .copy-button {
    opacity: 0.7;
}

.copy-button:hover {
    opacity: 1 !important;
}

.user-message .copy-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark-theme .user-message .copy-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.assistant-message .copy-button:hover {
    background-color: rgba(44, 240, 93, 0.815);
}

/* Link styling in chat messages */
.message a {
    color: #60a5fa;
    text-decoration: none;
    transition: var(--transition);
}

.message a:hover {
    color: #93c5fd;
    text-decoration: underline;
}

.light-theme .message a {
    color: #3b82f6;
}

.light-theme .message a:hover {
    color: #1d4ed8;
}

/* Code block styling */
pre {
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    overflow-x: auto;
    margin: var(--spacing-lg) 0;
    font-size: 0.9em;
    line-height: 1.5;
    background-color: var(--code-bg);
    border: 1px solid var(--code-border);
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Message code blocks - desktop responsive */
.message pre {
    background-color: var(--code-bg);
    border: 1px solid var(--code-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    overflow-x: auto;
    position: relative;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.4;
    max-width: 100%;
    box-sizing: border-box;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.message pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
}

code {
    font-family: var(--font-mono);
    padding: 0;
    border-radius: 0;
    background: transparent;
}

.code-copy-button {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: rgba(126, 126, 126, 0.1);
    color: var(--text-tertiary);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
    cursor: pointer;
    opacity: 0.6;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
}

.code-copy-button:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.2);
}

/* Input Area */
.input-container {
    padding: 24px;
    background-color: var(--chat-bg);
    border-top: 1px solid var(--border-color);
}

.input-area {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius-lg);
    padding: 12px 16px;
    transition: var(--transition);
    max-width: 800px;
    margin: 0 auto;
}

/* Desktop layout - hide mobile-specific containers */
@media (min-width: 769px) {
    .mobile-input-top-row,
    .mobile-input-bottom-row {
        display: contents;
    }

    .input-area {
        align-items: center;
    }

    .input-area textarea {
        min-height: 20px;
        padding: 8px 0;
    }
}

.input-area:focus-within {
    border-color: var(--input-focus-border);
}

.message-input-container {
    flex: 1;
    position: relative;
}

.input-area input,
.input-area textarea {
    width: 100%;
    padding: 8px 0;
    border: none;
    background: transparent;
    color: var(--text-color);
    font-size: 15px;
    outline: none;
    font-family: inherit;
    resize: none;
    overflow-y: auto;
}

.input-area textarea {
    min-height: 20px;
    max-height: 120px;
    line-height: 1.4;
    vertical-align: middle;
}

.input-area input::placeholder,
.input-area textarea::placeholder {
    color: var(--text-secondary);
}

.model-select {
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--input-border);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 14px;
    min-width: 140px;
    max-width: 180px;
    cursor: pointer;
    transition: var(--transition);
}

.model-select:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
}

.model-select option {
    background-color: var(--input-bg);
    color: var(--text-color);
}

.input-icon-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.input-icon {
    width: 20px;
    height: 20px;
    filter: brightness(0) saturate(100%) invert(56%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(95%) contrast(86%);
}

.send-button .input-icon {
    filter: brightness(0) saturate(100%) invert(76%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(100%) contrast(86%);
}

.action-button {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.action-button:hover {
    color: var(--primary-color);
    background-color: var(--background-color);
}

.send-button {
    color: var(--primary-color);
}

.stop-button {
    color: #ef4444;
}

/* File Preview */
.file-preview {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    background-color: var(--file-preview-bg);
    border-radius: var(--border-radius-md);
    display: none;
}

.preview-item {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--input-bg);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
}

.preview-item .file-content {
    margin-top: var(--spacing-md);
    white-space: pre-wrap;
    max-height: 200px;
    max-width: 100%;
    overflow: auto;
    padding: var(--spacing-md);
    background-color: var(--file-content-bg);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-mono);
    font-size: 0.9rem;
}

.view-preview, .remove-preview {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    margin-left: var(--spacing-sm);
    font-size: 1rem;
    opacity: 0.7;
    transition: var(--transition);
    color: var(--text-color);
}

.view-preview:hover, .remove-preview:hover {
    opacity: 1;
}

.remove-preview:hover {
    color: #ef4444;
}

.preview-item img {
    max-width: 100%;
    max-height: 200px;
    width: auto;
    height: auto;
    margin-right: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    object-fit: contain;
}

/* Drag and drop styles */
.input-container.drag-over {
    background-color: var(--accent-color-light, rgba(139, 92, 246, 0.1));
    border: 2px dashed var(--accent-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

/* Image processing indicator */
.processing-indicator {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    display: none;
    box-shadow: var(--shadow-md);
}

.processing-indicator i {
    margin-right: var(--spacing-xs);
}

/* Enhanced file preview for clipboard images */
.preview-item[data-clipboard="true"] {
    border-left: 3px solid var(--accent-color);
    position: relative;

}

.preview-item[data-clipboard="true"]::before {
    position: relative;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    font-size: 10px;
    opacity: 0.7;
}

/* Input container positioning for indicators */
.input-container {
    position: relative;
}

/* Code copy button */
.code-copy-button {
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    color: var(--text-tertiary);
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    cursor: pointer;
    opacity: 0.8;
    transition: var(--transition);
    z-index: 2;
    margin: 0 2px;
}

/* Code button container styling */
.code-button-container {
    position: absolute;
    bottom: 5px;
    left: 5px;
    display: flex;
    gap: 5px;
    width: auto;
    z-index: 10;
}

/* Remove the absolute positioning for buttons */
.code-button-container button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.code-copy-button:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm);
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    margin: 0 2px;
    background-color: var(--text-tertiary);
    border-radius: 50%;
    display: inline-block;
    opacity: 0.4;
}

.typing-indicator span:nth-child(1) {
    animation: pulse 1s infinite 0s;
}

.typing-indicator span:nth-child(2) {
    animation: pulse 1s infinite 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation: pulse 1s infinite 0.4s;
}

@keyframes pulse {
    0% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
    100% {
        opacity: 0.4;
        transform: scale(1);
    }
}

/* Responsive Design - Removed duplicate rules, kept only the final mobile section */

/* Additional styling for reference image match */
.assistant-message ul {
    margin: 12px 0;
    padding-left: 0;
    list-style: none;
}

.assistant-message li {
    margin: 8px 0;
    padding-left: 16px;
    position: relative;
}

.assistant-message li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--text-secondary);
}

/* Ensure proper avatar styling */
.assistant-avatar img {
    width: 20px;
    height: 20px;
    filter: brightness(0) saturate(100%) invert(56%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(95%) contrast(86%);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 4px;
}

/* Custom CodeMirror theme integration */
.dark-theme .CodeMirror {
    background-color: var(--editor-bg) !important;
    color: var(--text-color) !important;
}

.dark-theme .CodeMirror-gutters {
    background-color: var(--editor-header-bg) !important;
    border-right: 1px solid var(--border-color) !important;
}

.dark-theme .CodeMirror-linenumber {
    color: var(--text-tertiary) !important;
}

.light-theme .CodeMirror {
    background-color: var(--editor-bg) !important;
    color: var(--text-color) !important;
}

.light-theme .CodeMirror-gutters {
    background-color: var(--editor-header-bg) !important;
    border-right: 1px solid var(--border-color) !important;
}

.light-theme .CodeMirror-linenumber {
    color: var(--text-tertiary) !important;
}

.assistant-avatar {
    background-color: var(--message-assistant-bg);
    border: 1px solid var(--border-color);
}

.formatted-message {
    line-height: 1.4; /* Adjust line spacing */
}

/* Attribution Panel */
.attribution-panel {
    position: fixed;
    bottom: -100%;
    left: 0;
    right: 0;
    background-color: var(--background-color);
    border-top: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: bottom 0.3s ease;
    max-height: 70vh;
    overflow-y: auto;
}

.attribution-panel.open {
    bottom: 0;
}

.attribution-content {
    padding: var(--spacing-xl);
    max-width: 800px;
    margin: 0 auto;
}

.attribution-content h3 {
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.attribution-section {
    margin-bottom: var(--spacing-xl);
}

.attribution-section h4 {
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
}

.attribution-section ul {
    list-style: none;
    padding: 0;
}

.attribution-section li {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--message-assistant-bg);
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--primary-color);
}

.attribution-section li strong {
    color: var(--text-color);
    font-weight: 600;
}

.attribution-link {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.attribution-link a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.attribution-link a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.attribution-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-xl);
}

.attribution-footer p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-style: italic;
}

.close-attribution-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
}

.close-attribution-button:hover {
    background-color: var(--primary-hover);
}

/* Settings Panels */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background-color: var(--sidebar-bg);
    border-left: 1px solid var(--border-color);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);
    /* Ensure panels don't interfere with chat interaction */
    pointer-events: none;
}

.settings-panel.open {
    right: 0;
    pointer-events: auto;
}

/* Add backdrop for better visual separation */
.settings-panel.open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 400px;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: -1;
    pointer-events: auto;
}

/* API Key Management Styles */
.api-keys-section {
    margin: var(--spacing-lg) 0;
}

.api-key-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.api-key-input-group {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.api-key-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius-sm);
    color: var(--text-color);
    font-size: 14px;
    transition: var(--transition);
}

.api-key-input:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-shadow);
}

.test-connection-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    min-width: 60px;
}

.test-connection-btn:hover {
    background-color: var(--primary-hover);
}

.test-connection-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.api-key-help {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.api-key-help a {
    color: #60a5fa;
    text-decoration: none;
}

.api-key-help a:hover {
    text-decoration: underline;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-secondary);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--editor-header-bg);
}

.settings-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.close-panel-button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.close-panel-button:hover {
    color: var(--text-color);
    background-color: var(--input-bg);
}

.settings-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.setting-select,
.setting-textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.setting-select:focus,
.setting-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
}

.setting-textarea {
    resize: vertical;
    min-height: 80px;
    font-family: var(--font-mono);
}

.checkbox-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
}

.date-range {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.date-input {
    flex: 1;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--input-bg);
    color: var(--text-color);
}

.chat-selection {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    background-color: var(--input-bg);
}

.chat-selection-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.chat-selection-item:hover {
    background-color: var(--background-color);
}

.setting-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

.export-section,
.import-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.export-section:last-child,
.import-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.export-section h4,
.import-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
}

/* Code Integration Panel */
.overlay-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.panel-content {
    width: 90%;
    max-width: 1000px;
    height: 80%;
    background-color: var(--sidebar-bg);
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--editor-header-bg);
}

.panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
}

.code-preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.file-tabs {
    display: flex;
    background-color: var(--editor-header-bg);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.file-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
    white-space: nowrap;
}

.file-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.file-tab:hover {
    color: var(--text-color);
    background-color: var(--input-bg);
}

.diff-viewer {
    flex: 1;
    overflow: auto;
    padding: var(--spacing-md);
    background-color: var(--editor-bg);
    font-family: var(--font-mono);
    font-size: 0.9rem;
}

.diff-line {
    padding: 2px var(--spacing-xs);
    margin: 1px 0;
    border-radius: 2px;
}

.diff-line.added {
    background-color: rgba(34, 197, 94, 0.2);
    border-left: 3px solid #22c55e;
}

.diff-line.removed {
    background-color: rgba(239, 68, 68, 0.2);
    border-left: 3px solid #ef4444;
}

.diff-line.context {
    color: var(--text-secondary);
}

.panel-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--editor-header-bg);
}

.apply-code-button {
    background-color: #8b5cf6 !important;
    color: white !important;
    border: none !important;
    animation: pulse 2s infinite;
}

.apply-code-button:hover {
    background-color: #7c3aed !important;
    transform: scale(1.05);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(139, 92, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(139, 92, 246, 0); }
}

/* Notification styles */
.notification {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    /* Prevent horizontal overflow on mobile */
    body {
        overflow-x: hidden;
        max-width: 100vw;
    }

    .main-container {
        max-width: 100vw;
        overflow-x: hidden;
    }

    .chat-container {
        max-width: 100vw;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    .sidebar {
        position: fixed;
        left: -260px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
        box-shadow: var(--shadow-lg);
    }

    /* Mobile sidebar backdrop */
    .sidebar.open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 260px;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
        pointer-events: auto;
    }

    /* Fix chat history scrolling */
    .chat-history {
        flex: 1;
        overflow-y: auto;
        max-height: calc(100vh - 200px);
        padding: 0;
    }

    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        padding: 8px;
        border-radius: var(--border-radius-sm);
        transition: var(--transition);
        cursor: pointer;
    }

    .mobile-sidebar-toggle:hover {
        background-color: var(--input-bg);
    }

    .mobile-sidebar-toggle i {
        color: var(--text-color);
        font-size: 18px;
    }

    .chat-messages {
        padding: 16px;
        gap: 16px;
    }

    /* Fix code blocks in chat messages to be responsive */
    .message pre {
        overflow-x: auto;
        max-width: 100%;
        white-space: pre-wrap;
        word-wrap: break-word;
        margin: 0;
        padding: 12px;
        border-radius: 6px;
        box-sizing: border-box;
        -webkit-overflow-scrolling: touch;
    }

    .message pre code {
        white-space: pre-wrap;
        word-wrap: break-word;
        max-width: 100%;
        display: block;
        overflow-wrap: break-word;
        hyphens: auto;
        box-sizing: border-box;
    }

    .message code:not(pre code) {
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 100%;
        hyphens: auto;
    }

    /* Ensure all content in messages is responsive */
    .message * {
        max-width: 100%;
        box-sizing: border-box;
    }

    /* Mobile-friendly table wrapper */
    .message table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        font-size: 10px;
    }

    .message table th,
    .message table td {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        text-align: left;
        word-wrap: break-word;
        min-width: 100px;
    }

    /* Table wrapper for mobile responsiveness */
    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: 12px 0;
        padding: 4px;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-sm);
        background-color: var(--background-color);
        max-width: 300px;
        box-sizing: border-box;
    }

    .table-wrapper table {
        width: auto;
        min-width: 100%;
        white-space: nowrap;
        margin: 0;
    }

    .table-wrapper table th,
    .table-wrapper table td {
        white-space: normal;
        word-wrap: break-word;
    }

    .table-wrapper table {
        width: 100%;
        min-width: 300px;
        margin: 0;
        border-collapse: collapse;
        display: table;
        white-space: nowrap;
    }

    .table-wrapper table thead,
    .table-wrapper table tbody,
    .table-wrapper table tr {
        display: table-row-group;
        width: auto;
    }

    .table-wrapper table tr {
        display: table-row;
    }

    /* Responsive images in messages */
    .message img {
        max-width: 100%;
        height: auto;
        width: 10px;
        border-radius: var(--border-radius-sm);
        margin: 8px 0;
    }

    /* Responsive images in file previews */
    .preview-item img {
        max-width: 100%;
        max-height: 120px;
        width: auto;
        height: auto;
        margin: 4px 0;
        border-radius: var(--border-radius-sm);
        object-fit: contain;
        display: block;
    }

    /* Ensure file preview container doesn't overflow */
    .file-preview {
        max-width: 100%;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    .preview-item {
        max-width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        margin-bottom: 8px;
    }

    /* Improve filename display on mobile */
    .preview-item span {
        font-size: 0.85rem;
        line-height: 1.2;
        word-break: break-word;
        max-width: 150px;
    }

    /* Mobile code editor improvements - position from bottom */
    .code-editor-panel {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: auto;
        width: 100% !important;
        height: 100vh !important;
        max-height: 100vh;
        z-index: 1001;
        border-top: 2px solid var(--border-color);
        border-radius: 12px 12px 0 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
        transform: translateY(0);
        transition: transform 0.3s ease;
    }

    /* Add a visual handle at the top of mobile code editor */
    .code-editor-panel::before {
        content: '';
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background-color: var(--text-tertiary);
        border-radius: 2px;
        z-index: 10;
    }

    /* Ensure main content is visible behind code editor */
    .main-content {
        position: relative;
        z-index: 1;
    }

    .code-editor-controls {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        padding: 8px 12px;
    }

    .editor-tab-controls {
        margin-top: var(--spacing-sm);
        width: 100%;
        justify-content: center;
    }

    /* Mobile code editor layout - full width */
    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .editor-content {
        flex-direction: column;
        height: calc(100% - 60px);
    }

    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .editor-container,
    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .preview-container {
        width: 100% !important;
        height: auto;
        min-width: 100% !important;
        max-width: 100% !important;
    }

    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .editor-container {
        height: 50%;
        flex: 1;
    }

    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .preview-container {
        height: 50%;
        flex: 1;
    }

    .code-editor-panel:not(.fullscreen-preview).horizontal-layout .resize-handle {
        width: 100%;
        height: 6px;
        cursor: row-resize;
    }

    /* Ensure CodeMirror takes full width */
    .code-editor-panel .CodeMirror {
        width: 100% !important;
        height: 100% !important;
    }

    .message-container {
        gap: 12px;
    }

    .message {
        padding: 12px 16px;
        font-size: 14px;
        max-width: 100%;
        overflow-x: auto;
    }

    /* Improved mobile input area layout */
    .input-container {
        padding: 16px;
        background-color: var(--chat-bg);
        border-top: 1px solid var(--border-color);
        position: sticky;
        bottom: 0;
        z-index: 50;
        max-width: 100vw;
        box-sizing: border-box;
        overflow-x: hidden;
    }

    /* Hide input container when code editor is open on mobile */
    body.code-editor-open .input-container {
        z-index: 10;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .input-area {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        align-items: stretch;
        max-width: 100%;
        margin: 0;
        box-sizing: border-box;
    }

    /* Mobile input layout - show as flex containers */
    .mobile-input-top-row {
        display: flex !important;
        align-items: center;
        gap: 12px;
        justify-content: space-between;
    }

    .mobile-input-bottom-row {
        display: flex !important;
        align-items: flex-end;
        gap: 12px;
    }

    .message-input-container {
        flex: 1;
        min-height: 60px;
    }

    .message-input-container input,
    .message-input-container textarea {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 12px 0;
        min-height: 44px;
        line-height: 1.4;
        font-family: inherit;
    }

    .message-input-container textarea {
        min-height: 60px;
        max-height: 120px;
        resize: none;
        overflow-y: auto;
    }

    .model-select {
        flex: 1;
        min-width: 0;
        font-size: 14px;
        padding: 10px 12px;
    }

    .input-icon-button {
        padding: 12px;
        min-width: 44px;
        min-height: 44px;
    }

    .code-editor-panel {
        min-width: 100%;
    }

    .code-editor-controls {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .run-code-button,
    .share-to-chat-button,
    .fullscreen-preview-button,
    .copy-code-button,
    .download-code-button {
        padding: var(--spacing-xs);
        font-size: 0.8rem;
    }

    .settings-panel {
        width: 100%;
        right: -100%;
    }

    /* Mobile backdrop adjustment */
    .settings-panel.open::before {
        right: 0;
    }

    /* Ensure panels close properly on mobile */
    .settings-panel.open {
        z-index: 1001;
    }

    .panel-content {
        width: 95%;
        height: 90%;
    }

    .checkbox-group {
        grid-template-columns: 1fr;
    }

    .date-range {
        flex-direction: column;
        align-items: stretch;
    }

    .setting-actions {
        flex-direction: column;
    }

    .header-actions {
        gap: var(--spacing-xs);
    }

    .header-actions .icon-button {
        padding: 6px;
    }

    .header-actions .icon {
        width: 16px;
        height: 16px;
    }
}
