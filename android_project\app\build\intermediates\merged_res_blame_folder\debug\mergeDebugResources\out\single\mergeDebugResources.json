[{"merged": "com.jermescode.ai.app-debug-33:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.jermescode.ai.app-debug-33:/layout_activity_main.xml.flat", "source": "com.jermescode.ai.app-main-35:/layout/activity_main.xml"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.jermescode.ai.app-debug-33:/xml_backup_rules.xml.flat", "source": "com.jermescode.ai.app-main-35:/xml/backup_rules.xml"}, {"merged": "com.jermescode.ai.app-debug-33:/xml_file_paths.xml.flat", "source": "com.jermescode.ai.app-main-35:/xml/file_paths.xml"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "com.jermescode.ai.app-debug-33:/xml_network_security_config.xml.flat", "source": "com.jermescode.ai.app-main-35:/xml/network_security_config.xml"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.jermescode.ai.app-debug-33:/xml_data_extraction_rules.xml.flat", "source": "com.jermescode.ai.app-main-35:/xml/data_extraction_rules.xml"}, {"merged": "com.jermescode.ai.app-debug-33:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "com.jermescode.ai.app-main-35:/mipmap-xhdpi/ic_launcher_round.png"}]