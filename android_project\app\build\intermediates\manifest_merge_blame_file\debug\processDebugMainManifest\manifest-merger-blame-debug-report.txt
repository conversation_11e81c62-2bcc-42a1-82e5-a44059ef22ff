1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jermescode.ai"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for AI chat functionality -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Camera permission for photo capture -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:5-65
16-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:10:22-62
17
18    <uses-feature
18-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:5-85
19        android:name="android.hardware.camera"
19-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:19-57
20        android:required="false" />
20-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:11:58-82
21    <uses-feature
21-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:5-95
22        android:name="android.hardware.camera.autofocus"
22-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:19-67
23        android:required="false" />
23-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:12:68-92
24
25    <!-- Storage permissions for file uploads and chat history export/import -->
26    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:5-80
26-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:15:22-77
27    <uses-permission
27-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:5-17:38
28        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
28-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:16:22-78
29        android:maxSdkVersion="28" />
29-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:17:9-35
30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:5-76
30-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:18:22-73
31    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:5-75
31-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:19:22-72
32    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:5-75
32-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:20:22-72
33
34    <!-- File provider for sharing files -->
35    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:5-24:40
35-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:23:22-79
36
37    <permission
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.jermescode.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:26:5-60:19
44        android:allowBackup="true"
44-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:27:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:28:9-65
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:29:9-54
50        android:icon="@mipmap/ic_launcher"
50-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:30:9-43
51        android:label="@string/app_name"
51-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:31:9-41
52        android:networkSecurityConfig="@xml/network_security_config"
52-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:35:9-69
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:32:9-54
54        android:testOnly="true"
55        android:theme="@style/Theme.JermesaCodeAi.Splash"
55-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:33:9-58
56        android:usesCleartextTraffic="true" >
56-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:34:9-44
57        <activity
57-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:38:9-48:20
58            android:name="com.jermescode.ai.MainActivity"
58-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:39:13-41
59            android:configChanges="orientation|screenSize|keyboardHidden"
59-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:43:13-74
60            android:exported="true"
60-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:40:13-36
61            android:screenOrientation="unspecified"
61-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:42:13-52
62            android:theme="@style/Theme.JermesaCodeAi.Splash" >
62-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:41:13-62
63            <intent-filter>
63-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:44:13-47:29
64                <action android:name="android.intent.action.MAIN" />
64-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:17-69
64-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:45:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:46:17-77
66-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:46:27-74
67            </intent-filter>
68        </activity>
69
70        <!-- File provider for sharing files -->
71        <provider
72            android:name="androidx.core.content.FileProvider"
72-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:52:13-62
73            android:authorities="com.jermescode.ai.fileprovider"
73-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:53:13-64
74            android:exported="false"
74-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:54:13-37
75            android:grantUriPermissions="true" >
75-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:55:13-47
76            <meta-data
76-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:56:13-58:54
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:57:17-67
78                android:resource="@xml/file_paths" />
78-->D:\app_dev\JermesCode_Ai\android_project\app\src\main\AndroidManifest.xml:58:17-51
79        </provider>
80        <provider
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
81            android:name="androidx.startup.InitializationProvider"
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
82            android:authorities="com.jermescode.ai.androidx-startup"
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
83            android:exported="false" >
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
84            <meta-data
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.emoji2.text.EmojiCompatInitializer"
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
86                android:value="androidx.startup" />
86-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\64e999c457a740eff668786494c619dc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\002069901257091e303b084a0bc88eae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
93        </provider>
94
95        <receiver
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
96            android:name="androidx.profileinstaller.ProfileInstallReceiver"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
97            android:directBootAware="false"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
98            android:enabled="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
99            android:exported="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
100            android:permission="android.permission.DUMP" >
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
102                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
105                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
108                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
111                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c246771cf8f346add206c028cad5b7b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
112            </intent-filter>
113        </receiver>
114    </application>
115
116</manifest>
