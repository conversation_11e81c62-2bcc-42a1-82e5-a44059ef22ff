// Mobile Gesture Handler for Pull-to-Refresh and Swipe Navigation
class MobileGestureHandler {
    constructor() {
        this.pullToRefreshEnabled = true;
        this.swipeThreshold = 50; // Minimum distance for swipe
        this.pullThreshold = 60; // Reduced threshold for easier triggering
        this.isRefreshing = false;
        this.startY = 0;
        this.startX = 0;
        this.currentY = 0;
        this.currentX = 0;
        this.isPulling = false;
        this.isSwiping = false;

        this.initializePullToRefresh();
        this.initializeSwipeGestures();
        this.addManualRefreshButton();
    }
    
    initializePullToRefresh() {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;
        
        // Create pull-to-refresh indicator
        this.createRefreshIndicator();
        
        let touchStartY = 0;
        let touchCurrentY = 0;
        let isAtTop = false;
        
        chatMessages.addEventListener('touchstart', (e) => {
            if (this.isRefreshing) return;
            
            touchStartY = e.touches[0].clientY;
            isAtTop = chatMessages.scrollTop === 0;
            this.isPulling = false;
        }, { passive: true });
        
        chatMessages.addEventListener('touchmove', (e) => {
            if (this.isRefreshing || !isAtTop) return;

            touchCurrentY = e.touches[0].clientY;
            const pullDistance = touchCurrentY - touchStartY;

            if (pullDistance > 0 && isAtTop) {
                this.isPulling = true;
                this.updateRefreshIndicator(pullDistance);

                // Prevent default scrolling when pulling (more responsive)
                if (pullDistance > 5) {
                    e.preventDefault();
                }
            }
        }, { passive: false });
        
        chatMessages.addEventListener('touchend', (e) => {
            if (!this.isPulling || this.isRefreshing) return;
            
            const pullDistance = touchCurrentY - touchStartY;
            
            if (pullDistance > this.pullThreshold) {
                this.triggerRefresh();
            } else {
                this.resetRefreshIndicator();
            }
            
            this.isPulling = false;
        }, { passive: true });
    }
    
    createRefreshIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'pull-refresh-indicator';
        indicator.innerHTML = `
            <div class="refresh-spinner"></div>
            <span class="refresh-text">Pull to refresh</span>
        `;
        indicator.style.cssText = `
            position: absolute;
            top: -60px;
            left: 0;
            right: 0;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background: var(--bg-color);
            color: var(--text-secondary);
            font-size: 14px;
            z-index: 10;
            transition: transform 0.3s ease;
        `;
        
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.style.position = 'relative';
            chatMessages.appendChild(indicator);
        }
        
        // Add spinner styles
        const style = document.createElement('style');
        style.textContent = `
            .refresh-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid var(--border-color);
                border-top: 2px solid var(--primary-color);
                border-radius: 50%;
                animation: spin 1s linear infinite;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .refresh-indicator-active .refresh-spinner {
                opacity: 1;
            }
        `;
        document.head.appendChild(style);
    }
    
    updateRefreshIndicator(pullDistance) {
        const indicator = document.getElementById('pull-refresh-indicator');
        if (!indicator) return;
        
        const progress = Math.min(pullDistance / this.pullThreshold, 1);
        const translateY = Math.min(pullDistance * 0.5, 60);
        
        indicator.style.transform = `translateY(${translateY}px)`;
        
        const text = indicator.querySelector('.refresh-text');
        if (text) {
            text.textContent = progress >= 1 ? 'Release to refresh' : 'Pull to refresh';
        }
        
        if (progress >= 1) {
            indicator.classList.add('refresh-indicator-active');
        } else {
            indicator.classList.remove('refresh-indicator-active');
        }
    }
    
    triggerRefresh() {
        if (this.isRefreshing) return;

        this.isRefreshing = true;
        const indicator = document.getElementById('pull-refresh-indicator');

        if (indicator) {
            indicator.style.transform = 'translateY(60px)';
            indicator.classList.add('refresh-indicator-active');

            const text = indicator.querySelector('.refresh-text');
            if (text) {
                text.textContent = 'Refreshing...';
            }
        }

        // Perform actual app refresh
        this.performAppRefresh().then(() => {
            this.completeRefresh();
            this.showRefreshComplete();
        }).catch((error) => {
            console.error('Refresh failed:', error);
            this.completeRefresh();
            this.showRefreshError();
        });
    }
    
    completeRefresh() {
        this.isRefreshing = false;
        this.resetRefreshIndicator();
    }
    
    resetRefreshIndicator() {
        const indicator = document.getElementById('pull-refresh-indicator');
        if (!indicator) return;
        
        indicator.style.transform = 'translateY(0)';
        indicator.classList.remove('refresh-indicator-active');
        
        const text = indicator.querySelector('.refresh-text');
        if (text) {
            text.textContent = 'Pull to refresh';
        }
    }
    
    async performAppRefresh() {
        // Perform actual app refresh operations
        try {
            // Reload models if available
            if (window.loadModels && typeof window.loadModels === 'function') {
                await window.loadModels();
            }

            // Refresh chat history display
            if (window.renderChatHistory && typeof window.renderChatHistory === 'function') {
                window.renderChatHistory();
            }

            // Clear any temporary states
            if (window.currentAttachments) {
                window.currentAttachments = [];
            }

            // Clear file preview if exists
            const filePreview = document.getElementById('file-preview');
            if (filePreview) {
                filePreview.innerHTML = '';
                filePreview.style.display = 'none';
            }

            // Scroll to bottom of chat
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Small delay to simulate refresh
            await new Promise(resolve => setTimeout(resolve, 800));

        } catch (error) {
            console.error('Error during app refresh:', error);
            throw error;
        }
    }

    showRefreshComplete() {
        this.showMessage('✓ App Refreshed', 'var(--primary-color)');
    }

    showRefreshError() {
        this.showMessage('⚠ Refresh Failed', '#ef4444');
    }

    showMessage(text, backgroundColor) {
        // Create a temporary message
        const message = document.createElement('div');
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${backgroundColor};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;

        document.body.appendChild(message);

        // Animate in
        setTimeout(() => {
            message.style.opacity = '1';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }, 2000);
    }

    addManualRefreshButton() {
        // Add a manual refresh button to the header
        const header = document.querySelector('.header');
        if (!header) return;

        const refreshButton = document.createElement('button');
        refreshButton.id = 'manual-refresh-button';
        refreshButton.className = 'icon-button';
        refreshButton.title = 'Refresh App';
        refreshButton.innerHTML = '<img src="icons/refresh.svg" alt="Refresh" class="icon">';
        refreshButton.style.cssText = `
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        refreshButton.addEventListener('click', () => {
            if (!this.isRefreshing) {
                this.manualRefresh();
            }
        });

        // Add to header actions
        const headerActions = header.querySelector('.header-actions');
        if (headerActions) {
            headerActions.insertBefore(refreshButton, headerActions.firstChild);
        } else {
            header.appendChild(refreshButton);
        }
    }

    async manualRefresh() {
        if (this.isRefreshing) return;

        this.isRefreshing = true;
        const refreshButton = document.getElementById('manual-refresh-button');

        // Add spinning animation to button
        if (refreshButton) {
            refreshButton.style.opacity = '0.6';
            refreshButton.style.transform = 'rotate(0deg)';
            refreshButton.style.transition = 'transform 1s linear';

            // Start spinning animation
            let rotation = 0;
            const spinInterval = setInterval(() => {
                rotation += 30;
                refreshButton.style.transform = `rotate(${rotation}deg)`;
            }, 50);

            // Stop spinning after refresh
            setTimeout(() => {
                clearInterval(spinInterval);
                refreshButton.style.opacity = '1';
                refreshButton.style.transform = 'rotate(0deg)';
                refreshButton.style.transition = 'background-color 0.2s ease';
            }, 1000);
        }

        try {
            await this.performAppRefresh();
            this.showRefreshComplete();
        } catch (error) {
            console.error('Manual refresh failed:', error);
            this.showRefreshError();
        } finally {
            this.isRefreshing = false;
        }
    }

    initializeSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let startTime = 0;
        let isSwipeGesture = false;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
            isSwipeGesture = false;
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            if (this.isPulling || this.isRefreshing) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const deltaX = currentX - startX;
            const deltaY = currentY - startY;

            // Determine if this is a horizontal swipe gesture
            if (Math.abs(deltaX) > 20 && Math.abs(deltaX) > Math.abs(deltaY) * 2) {
                isSwipeGesture = true;

                // Only prevent default for swipe gestures to avoid interfering with scrolling
                if (Math.abs(deltaX) > this.swipeThreshold / 2) {
                    e.preventDefault();
                }
            }
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            if (this.isPulling || this.isRefreshing || !isSwipeGesture) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // Check if it's a valid swipe (fast enough, far enough, and mostly horizontal)
            if (deltaTime < 500 &&
                Math.abs(deltaX) > this.swipeThreshold &&
                Math.abs(deltaY) < 150 &&
                Math.abs(deltaX) > Math.abs(deltaY)) {

                const sidebar = document.querySelector('.sidebar');
                const isCurrentlyOpen = sidebar && sidebar.classList.contains('open');

                if (deltaX > 0 && !isCurrentlyOpen) {
                    // Swipe right - open sidebar (only if not already open)
                    this.openSidebar();
                } else if (deltaX < 0 && isCurrentlyOpen) {
                    // Swipe left - close sidebar (only if currently open)
                    this.closeSidebar();
                }
            }

            isSwipeGesture = false;
        }, { passive: true });
    }
    
    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && !sidebar.classList.contains('open')) {
            sidebar.classList.add('open');
            console.log('Sidebar opened via swipe gesture');

            // Add visual feedback
            this.showGestureSuccess('Sidebar opened');
        }
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && sidebar.classList.contains('open')) {
            sidebar.classList.remove('open');
            console.log('Sidebar closed via swipe gesture');

            // Add visual feedback
            this.showGestureSuccess('Sidebar closed');
        }
    }

    showGestureSuccess(message) {
        // Create a temporary success message for gesture feedback
        const feedback = document.createElement('div');
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;

        document.body.appendChild(feedback);

        // Animate in
        setTimeout(() => {
            feedback.style.opacity = '0.9';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            feedback.style.opacity = '0';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 1000);
    }
}

// Initialize gesture handler when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on mobile devices
    if (window.innerWidth <= 768) {
        window.mobileGestureHandler = new MobileGestureHandler();
    }
});

// Re-initialize on window resize if switching to mobile
window.addEventListener('resize', () => {
    if (window.innerWidth <= 768 && !window.mobileGestureHandler) {
        window.mobileGestureHandler = new MobileGestureHandler();
    } else if (window.innerWidth > 768 && window.mobileGestureHandler) {
        // Clean up mobile gestures on desktop
        window.mobileGestureHandler = null;
    }
});
