<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Basic Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Script Loading Test</h2>
        <button onclick="testScriptLoading()">Test Script Loading</button>
        <div id="script-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. DOM Elements Test</h2>
        <button onclick="testDOMElements()">Test DOM Elements</button>
        <div id="dom-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Manager Initialization Test</h2>
        <button onclick="testManagerInit()">Test Manager Initialization</button>
        <div id="manager-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Basic Functions Test</h2>
        <button onclick="testBasicFunctions()">Test Basic Functions</button>
        <div id="functions-result"></div>
    </div>

    <!-- Include the same scripts as the main app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
    <script src="marked.min.js"></script>
    <script src="ai-providers.js"></script>
    <script src="ai-persona.js"></script>
    <script src="code-integration.js"></script>
    <script src="export-import.js"></script>

    <script>
        function showResult(container, type, message) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function testScriptLoading() {
            const result = document.getElementById('script-result');
            result.innerHTML = '';
            
            // Test if classes are available
            const tests = [
                { name: 'AIProviderManager', exists: typeof AIProviderManager !== 'undefined' },
                { name: 'AIPersonaManager', exists: typeof AIPersonaManager !== 'undefined' },
                { name: 'ExportImportManager', exists: typeof ExportImportManager !== 'undefined' },
                { name: 'CodeIntegrationManager', exists: typeof CodeIntegrationManager !== 'undefined' },
                { name: 'marked', exists: typeof marked !== 'undefined' },
                { name: 'hljs', exists: typeof hljs !== 'undefined' }
            ];
            
            tests.forEach(test => {
                const type = test.exists ? 'success' : 'error';
                const status = test.exists ? '✅' : '❌';
                showResult(result, type, `${status} ${test.name} ${test.exists ? 'loaded' : 'not found'}`);
            });
        }
        
        function testDOMElements() {
            const result = document.getElementById('dom-result');
            result.innerHTML = '';
            
            // Create a temporary iframe with the main app to test DOM elements
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'index.html';
            document.body.appendChild(iframe);
            
            iframe.onload = () => {
                const doc = iframe.contentDocument;
                const elements = [
                    'model-select',
                    'message-input',
                    'send-button',
                    'chat-display',
                    'file-input',
                    'attach-button'
                ];
                
                elements.forEach(id => {
                    const element = doc.getElementById(id);
                    const type = element ? 'success' : 'error';
                    const status = element ? '✅' : '❌';
                    showResult(result, type, `${status} Element #${id} ${element ? 'found' : 'not found'}`);
                });
                
                document.body.removeChild(iframe);
            };
        }
        
        function testManagerInit() {
            const result = document.getElementById('manager-result');
            result.innerHTML = '';
            
            try {
                // Test AI Provider Manager
                if (typeof AIProviderManager !== 'undefined') {
                    const providerManager = new AIProviderManager();
                    showResult(result, 'success', '✅ AIProviderManager instantiated successfully');
                    
                    // Test methods
                    const providers = providerManager.getAvailableProviders();
                    showResult(result, 'info', `📋 Available providers: ${providers.join(', ')}`);
                    
                    const requiresKey = providerManager.requiresApiKey('openai');
                    showResult(result, 'info', `🔑 OpenAI requires API key: ${requiresKey}`);
                    
                    const displayName = providerManager.getProviderDisplayName('ollama');
                    showResult(result, 'info', `🏷️ Ollama display name: ${displayName}`);
                } else {
                    showResult(result, 'error', '❌ AIProviderManager not available');
                }
                
                // Test AI Persona Manager
                if (typeof AIPersonaManager !== 'undefined') {
                    const personaManager = new AIPersonaManager();
                    showResult(result, 'success', '✅ AIPersonaManager instantiated successfully');
                } else {
                    showResult(result, 'error', '❌ AIPersonaManager not available');
                }
                
            } catch (error) {
                showResult(result, 'error', `❌ Manager initialization failed: ${error.message}`);
            }
        }
        
        function testBasicFunctions() {
            const result = document.getElementById('functions-result');
            result.innerHTML = '';
            
            try {
                // Test message formatting functions
                const providerManager = new AIProviderManager();
                
                const testMessage = {
                    role: 'user',
                    content: 'Test message',
                    images: ['dGVzdA=='], // base64 for "test"
                    imageType: 'image/png'
                };
                
                // Test OpenAI format
                const openaiFormat = providerManager.convertMessagesToOpenAIFormat([testMessage]);
                if (openaiFormat && openaiFormat.length > 0) {
                    showResult(result, 'success', '✅ OpenAI message format conversion works');
                } else {
                    showResult(result, 'error', '❌ OpenAI message format conversion failed');
                }
                
                // Test Gemini format
                const geminiFormat = providerManager.convertMessagesToGeminiFormat([testMessage]);
                if (geminiFormat && geminiFormat.length > 0) {
                    showResult(result, 'success', '✅ Gemini message format conversion works');
                } else {
                    showResult(result, 'error', '❌ Gemini message format conversion failed');
                }
                
                // Test Ollama format
                const ollamaFormat = providerManager.convertMessagesToOllamaFormat([testMessage]);
                if (ollamaFormat && ollamaFormat.length > 0) {
                    showResult(result, 'success', '✅ Ollama message format conversion works');
                } else {
                    showResult(result, 'error', '❌ Ollama message format conversion failed');
                }
                
            } catch (error) {
                showResult(result, 'error', `❌ Function test failed: ${error.message}`);
            }
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                testScriptLoading();
                testManagerInit();
                testBasicFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
