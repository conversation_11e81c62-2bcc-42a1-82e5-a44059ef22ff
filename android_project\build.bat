@echo off
echo Building JermesaCode Ai Android App...
echo.

echo Cleaning previous builds...
call gradlew.bat clean

echo Building debug APK...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Build successful!
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo To install on device, run:
    echo gradlew.bat installDebug
) else (
    echo.
    echo ✗ Build failed!
    echo Check the error messages above.
)

pause
