<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#FF4081</color>
    <color name="background_color">#FAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="error_color">#F44336</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="splash_background">#1E1E1E</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">JermesaCode Ai</string>
    <string name="download_started">Download started</string>
    <string name="error_loading">Error loading page</string>
    <string name="file_upload_error">Error uploading file</string>
    <string name="loading">Loading...</string>
    <string name="permission_camera_rationale">Camera permission is needed to capture photos for the AI chat.</string>
    <string name="permission_denied">Permission denied. Some features may not work properly.</string>
    <string name="permission_storage_rationale">Storage permission is needed to save and load chat history.</string>
    <style name="Base.Theme.JermesaCodeAi" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorError">@color/error_color</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:navigationBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    <style name="Theme.JermesaCodeAi" parent="Base.Theme.JermesaCodeAi"/>
    <style name="Theme.JermesaCodeAi.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splash_background</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.JermesaCodeAi</item>
    </style>
</resources>