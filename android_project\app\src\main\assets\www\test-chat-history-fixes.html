<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat History Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .info {
            background-color: #17a2b8;
            color: white;
        }
        #test-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 10px;
            background-color: #1e1e1e;
        }
    </style>
</head>
<body>
    <h1>Chat History Fixes Test Suite</h1>
    <p>This page tests all the chat history fixes to ensure they work correctly.</p>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        <button class="test-button" onclick="resetTestData()">Reset Test Data</button>
    </div>

    <div class="test-section">
        <h2>Individual Tests</h2>
        <button class="test-button" onclick="testNewChatFunction()">Test New Chat Function</button>
        <button class="test-button" onclick="testLoadChatFunction()">Test Load Chat Function</button>
        <button class="test-button" onclick="testChatSaving()">Test Chat Saving</button>
        <button class="test-button" onclick="testChatHistoryRendering()">Test Chat History Rendering</button>
        <button class="test-button" onclick="testReloadFunction()">Test Reload Function</button>
        <button class="test-button" onclick="testImportExport()">Test Import/Export</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // Test utilities
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`TEST: ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Mock data for testing
        const testChats = {
            'test-chat-1': {
                id: 'test-chat-1',
                title: 'Test Chat 1',
                history: [
                    { role: 'user', content: 'Hello, this is test chat 1' },
                    { role: 'assistant', content: 'Hi! This is a response from test chat 1.' }
                ],
                model: 'test-model',
                timestamp: Date.now() - 3600000 // 1 hour ago
            },
            'test-chat-2': {
                id: 'test-chat-2',
                title: 'Test Chat 2',
                history: [
                    { role: 'user', content: 'Hello, this is test chat 2' },
                    { role: 'assistant', content: 'Hi! This is a response from test chat 2.' }
                ],
                model: 'test-model',
                timestamp: Date.now() - 1800000 // 30 minutes ago
            }
        };

        function resetTestData() {
            localStorage.setItem('ollama-chats', JSON.stringify(testChats));
            logResult('Test data reset with 2 sample chats', 'info');
        }

        // Test functions
        function testNewChatFunction() {
            logResult('Testing New Chat Function...', 'info');
            
            try {
                // Check if newChat function exists
                if (typeof window.parent.newChat === 'function') {
                    logResult('✓ newChat function exists', 'success');
                } else {
                    logResult('✗ newChat function not found', 'error');
                    return;
                }

                // Test would require access to parent window functions
                logResult('✓ New Chat function test completed (requires manual verification)', 'success');
            } catch (error) {
                logResult(`✗ New Chat test failed: ${error.message}`, 'error');
            }
        }

        function testLoadChatFunction() {
            logResult('Testing Load Chat Function...', 'info');
            
            try {
                // Check if loadChat function exists
                if (typeof window.parent.loadChat === 'function') {
                    logResult('✓ loadChat function exists', 'success');
                } else {
                    logResult('✗ loadChat function not found', 'error');
                    return;
                }

                logResult('✓ Load Chat function test completed (requires manual verification)', 'success');
            } catch (error) {
                logResult(`✗ Load Chat test failed: ${error.message}`, 'error');
            }
        }

        function testChatSaving() {
            logResult('Testing Chat Saving...', 'info');
            
            try {
                const testChat = {
                    id: 'save-test-' + Date.now(),
                    title: 'Save Test Chat',
                    history: [
                        { role: 'user', content: 'Test save message' },
                        { role: 'assistant', content: 'Test save response' }
                    ],
                    model: 'test-model',
                    timestamp: Date.now()
                };

                // Save to localStorage
                const existingChats = JSON.parse(localStorage.getItem('ollama-chats') || '{}');
                existingChats[testChat.id] = testChat;
                localStorage.setItem('ollama-chats', JSON.stringify(existingChats));

                // Verify save
                const savedChats = JSON.parse(localStorage.getItem('ollama-chats') || '{}');
                if (savedChats[testChat.id]) {
                    logResult('✓ Chat saving works correctly', 'success');
                } else {
                    logResult('✗ Chat saving failed', 'error');
                }
            } catch (error) {
                logResult(`✗ Chat saving test failed: ${error.message}`, 'error');
            }
        }

        function testChatHistoryRendering() {
            logResult('Testing Chat History Rendering...', 'info');
            
            try {
                const savedChats = JSON.parse(localStorage.getItem('ollama-chats') || '{}');
                const chatCount = Object.keys(savedChats).length;
                
                if (chatCount > 0) {
                    logResult(`✓ Found ${chatCount} chats in storage`, 'success');
                    
                    // Check if chats have required properties
                    let validChats = 0;
                    for (const chat of Object.values(savedChats)) {
                        if (chat.id && chat.title && chat.history && chat.timestamp) {
                            validChats++;
                        }
                    }
                    
                    if (validChats === chatCount) {
                        logResult('✓ All chats have required properties', 'success');
                    } else {
                        logResult(`✗ ${chatCount - validChats} chats missing required properties`, 'error');
                    }
                } else {
                    logResult('! No chats found in storage', 'info');
                }
            } catch (error) {
                logResult(`✗ Chat history rendering test failed: ${error.message}`, 'error');
            }
        }

        function testReloadFunction() {
            logResult('Testing Reload Function...', 'info');
            
            try {
                // Check if reloadChat function exists
                if (typeof window.parent.reloadChat === 'function') {
                    logResult('✓ reloadChat function exists', 'success');
                } else {
                    logResult('✗ reloadChat function not found', 'error');
                    return;
                }

                logResult('✓ Reload function test completed (requires manual verification)', 'success');
            } catch (error) {
                logResult(`✗ Reload function test failed: ${error.message}`, 'error');
            }
        }

        function testImportExport() {
            logResult('Testing Import/Export Functionality...', 'info');
            
            try {
                // Test export data format
                const savedChats = JSON.parse(localStorage.getItem('ollama-chats') || '{}');
                const exportData = {
                    exportDate: new Date().toISOString(),
                    chats: Object.values(savedChats)
                };

                // Test if export data is valid JSON
                const exportJson = JSON.stringify(exportData);
                const parsedData = JSON.parse(exportJson);
                
                if (parsedData.chats && Array.isArray(parsedData.chats)) {
                    logResult('✓ Export data format is valid', 'success');
                } else {
                    logResult('✗ Export data format is invalid', 'error');
                }

                // Test import simulation
                if (parsedData.chats.length > 0) {
                    logResult(`✓ Export contains ${parsedData.chats.length} chats`, 'success');
                } else {
                    logResult('! Export contains no chats', 'info');
                }

            } catch (error) {
                logResult(`✗ Import/Export test failed: ${error.message}`, 'error');
            }
        }

        function runAllTests() {
            logResult('=== Starting All Tests ===', 'info');
            clearResults();
            
            setTimeout(() => testNewChatFunction(), 100);
            setTimeout(() => testLoadChatFunction(), 200);
            setTimeout(() => testChatSaving(), 300);
            setTimeout(() => testChatHistoryRendering(), 400);
            setTimeout(() => testReloadFunction(), 500);
            setTimeout(() => testImportExport(), 600);
            
            setTimeout(() => {
                logResult('=== All Tests Completed ===', 'info');
            }, 700);
        }

        // Initialize test data on load
        window.addEventListener('load', () => {
            logResult('Chat History Test Suite Loaded', 'info');
            resetTestData();
        });
    </script>
</body>
</html>
