<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Handling Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #paste-area {
            width: 100%;
            height: 100px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            cursor: pointer;
        }
        #paste-area.drag-over {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Image Handling Test Suite</h1>
    
    <div class="test-section">
        <h2>1. Clipboard Paste Test</h2>
        <p>Copy an image to your clipboard and paste it in the area below:</p>
        <div id="paste-area" tabindex="0">
            Click here and paste an image (Ctrl+V)
        </div>
        <div id="paste-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. File Upload Test</h2>
        <input type="file" id="file-input" accept="image/*" multiple>
        <div id="upload-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Provider Format Test</h2>
        <button onclick="testProviderFormats()">Test Provider Message Formats</button>
        <div id="provider-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Image Compression Test</h2>
        <input type="file" id="compression-input" accept="image/*">
        <div id="compression-result"></div>
    </div>

    <script>
        // Test clipboard paste functionality
        const pasteArea = document.getElementById('paste-area');
        const pasteResult = document.getElementById('paste-result');
        
        pasteArea.addEventListener('paste', async (e) => {
            e.preventDefault();
            const items = Array.from(e.clipboardData.items);
            
            for (const item of items) {
                if (item.type.startsWith('image/')) {
                    const file = item.getAsFile();
                    if (file) {
                        showResult(pasteResult, 'success', `✅ Image pasted: ${file.name || 'clipboard-image'} (${(file.size/1024).toFixed(1)}KB)`);
                        
                        // Show preview
                        const img = document.createElement('img');
                        img.className = 'image-preview';
                        img.src = URL.createObjectURL(file);
                        pasteResult.appendChild(img);
                        
                        // Test compression
                        try {
                            const compressed = await compressImageIfNeeded(file);
                            showResult(pasteResult, 'info', `📦 Compressed: ${(compressed.size/1024).toFixed(1)}KB`);
                        } catch (error) {
                            showResult(pasteResult, 'error', `❌ Compression failed: ${error.message}`);
                        }
                    }
                    break;
                }
            }
        });
        
        // Test file upload
        document.getElementById('file-input').addEventListener('change', async (e) => {
            const files = Array.from(e.target.files);
            const uploadResult = document.getElementById('upload-result');
            uploadResult.innerHTML = '';
            
            for (const file of files) {
                showResult(uploadResult, 'success', `✅ File selected: ${file.name} (${(file.size/1024).toFixed(1)}KB)`);
                
                try {
                    const compressed = await compressImageIfNeeded(file);
                    showResult(uploadResult, 'info', `📦 Compressed: ${(compressed.size/1024).toFixed(1)}KB`);
                } catch (error) {
                    showResult(uploadResult, 'error', `❌ Compression failed: ${error.message}`);
                }
            }
        });
        
        // Test provider formats
        function testProviderFormats() {
            const result = document.getElementById('provider-result');
            result.innerHTML = '';
            
            const testMessage = {
                role: 'user',
                content: 'Test image message',
                images: ['iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='],
                imageType: 'image/png'
            };
            
            // Test OpenAI format
            try {
                const openaiFormat = convertToOpenAIFormat([testMessage]);
                showResult(result, 'success', '✅ OpenAI format conversion successful');
                console.log('OpenAI format:', openaiFormat);
            } catch (error) {
                showResult(result, 'error', `❌ OpenAI format failed: ${error.message}`);
            }
            
            // Test Gemini format
            try {
                const geminiFormat = convertToGeminiFormat([testMessage]);
                showResult(result, 'success', '✅ Gemini format conversion successful');
                console.log('Gemini format:', geminiFormat);
            } catch (error) {
                showResult(result, 'error', `❌ Gemini format failed: ${error.message}`);
            }
            
            // Test Ollama format
            try {
                const ollamaFormat = convertToOllamaFormat([testMessage]);
                showResult(result, 'success', '✅ Ollama format conversion successful');
                console.log('Ollama format:', ollamaFormat);
            } catch (error) {
                showResult(result, 'error', `❌ Ollama format failed: ${error.message}`);
            }
        }
        
        // Helper functions (simplified versions of the main app functions)
        function showResult(container, type, message) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        async function compressImageIfNeeded(file, maxWidth = 1024, maxHeight = 1024, quality = 0.8) {
            return new Promise((resolve) => {
                if (file.size < 1024 * 1024) {
                    resolve(file);
                    return;
                }
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = () => {
                    let { width, height } = img;
                    
                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }
                    
                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(img, 0, 0, width, height);
                    
                    canvas.toBlob((blob) => {
                        const compressedFile = new File([blob], file.name, {
                            type: 'image/jpeg',
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    }, 'image/jpeg', quality);
                };
                
                img.onerror = () => resolve(file);
                img.src = URL.createObjectURL(file);
            });
        }
        
        function convertToOpenAIFormat(messages) {
            return messages.map(msg => {
                if (msg.images && msg.images.length > 0) {
                    const content = [{ type: "text", text: msg.content }];
                    msg.images.forEach(imageBase64 => {
                        content.push({
                            type: "image_url",
                            image_url: {
                                url: `data:${msg.imageType || 'image/jpeg'};base64,${imageBase64}`,
                                detail: "auto"
                            }
                        });
                    });
                    return { role: msg.role, content: content };
                }
                return { role: msg.role, content: msg.content };
            });
        }
        
        function convertToGeminiFormat(messages) {
            return messages.filter(msg => msg.role !== 'system').map(msg => {
                const parts = [{ text: msg.content }];
                if (msg.images && msg.images.length > 0) {
                    msg.images.forEach(imageBase64 => {
                        parts.push({
                            inline_data: {
                                mime_type: msg.imageType || "image/jpeg",
                                data: imageBase64
                            }
                        });
                    });
                }
                return {
                    role: msg.role === 'assistant' ? 'model' : 'user',
                    parts: parts
                };
            });
        }
        
        function convertToOllamaFormat(messages) {
            return messages.map(msg => {
                if (msg.images && msg.images.length > 0) {
                    return {
                        role: msg.role,
                        content: msg.content,
                        images: msg.images
                    };
                }
                return { role: msg.role, content: msg.content };
            });
        }
    </script>
</body>
</html>
