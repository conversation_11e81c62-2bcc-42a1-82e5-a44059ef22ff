<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Smart Code Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .code-block {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Smart Code Integration Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>To test the Smart Code Integration feature:</p>
        <ol>
            <li>Open the main application (index.html)</li>
            <li>Start a conversation with the AI</li>
            <li>Ask the AI to provide code examples or modifications</li>
            <li>Look for the magic wand button (✨) to appear next to the send button</li>
            <li>Click the magic wand button to apply code changes</li>
            <li>Open the code editor to see if changes are applied</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Sample AI Prompts to Test</h2>
        <div class="code-block">
            <strong>Prompt 1:</strong> "Create a simple HTML page with a button"
        </div>
        <div class="code-block">
            <strong>Prompt 2:</strong> "Write a JavaScript function that adds two numbers"
        </div>
        <div class="code-block">
            <strong>Prompt 3:</strong> "Create CSS styles for a responsive navigation bar"
        </div>
        <div class="code-block">
            <strong>Prompt 4:</strong> "Modify the following code to add error handling"
        </div>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li>✅ Magic wand button appears when AI provides code</li>
            <li>✅ Clicking the button opens a preview panel</li>
            <li>✅ Preview panel shows detected code changes</li>
            <li>✅ "Apply Changes" button works correctly</li>
            <li>✅ Code is applied to the editor with correct language</li>
            <li>✅ Success notification appears</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Sample Code Response</h2>
        <p>Here's an example of what the AI might respond with:</p>
        <pre><code class="language-javascript">
function addNumbers(a, b) {
    return a + b;
}

// Usage example
const result = addNumbers(5, 3);
console.log(result); // Output: 8
        </code></pre>
        <p>This should trigger the Smart Code Integration to show the apply button.</p>
    </div>

    <script>
        // Simple test to verify the page loads
        console.log('Test page loaded successfully');
        
        // Check if we can access the main application
        if (window.opener) {
            console.log('Test page opened from main application');
        }
    </script>
</body>
</html>
