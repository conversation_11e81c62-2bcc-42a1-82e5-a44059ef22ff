<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Chat System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .info {
            background-color: #17a2b8;
            color: white;
        }
        #test-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 10px;
            background-color: #1e1e1e;
        }
    </style>
</head>
<body>
    <h1>Quick Chat System Test</h1>
    <p>This test verifies that the chat system is working correctly after the rewrite.</p>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testChatSystemBasics()">Test Chat System Basics</button>
        <button class="test-button" onclick="testMessageHandling()">Test Message Handling</button>
        <button class="test-button" onclick="testSessionManagement()">Test Session Management</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // Test utilities
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`TEST: ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Test functions
        function testChatSystemBasics() {
            logResult('Testing Chat System Basics...', 'info');
            
            try {
                // Test if we can access the parent window
                if (!window.parent || window.parent === window) {
                    logResult('! Running in standalone mode, cannot test parent window functions', 'info');
                    return;
                }

                // Test if new chat system variables exist
                const hasCurrentSession = typeof window.parent.currentChatSession !== 'undefined';
                const hasAllSessions = typeof window.parent.allChatSessions !== 'undefined';
                
                if (hasCurrentSession) {
                    logResult('✓ currentChatSession variable exists', 'success');
                } else {
                    logResult('✗ currentChatSession variable missing', 'error');
                }

                if (hasAllSessions) {
                    logResult('✓ allChatSessions variable exists', 'success');
                } else {
                    logResult('✗ allChatSessions variable missing', 'error');
                }

                // Test if core functions exist
                const functions = [
                    'createNewChatSession',
                    'loadChatSession', 
                    'saveChatSession',
                    'renderChatSessionsList',
                    'addMessage',
                    'sendMessage'
                ];

                let functionsFound = 0;
                functions.forEach(funcName => {
                    if (typeof window.parent[funcName] === 'function') {
                        logResult(`✓ Function ${funcName} exists`, 'success');
                        functionsFound++;
                    } else {
                        logResult(`✗ Function ${funcName} missing`, 'error');
                    }
                });

                if (functionsFound === functions.length) {
                    logResult('✅ All core chat functions are available', 'success');
                } else {
                    logResult(`⚠️ ${functionsFound}/${functions.length} core functions found`, 'error');
                }

            } catch (error) {
                logResult(`✗ Chat system basics test failed: ${error.message}`, 'error');
            }
        }

        function testMessageHandling() {
            logResult('Testing Message Handling...', 'info');
            
            try {
                if (!window.parent || window.parent === window) {
                    logResult('! Cannot test message handling in standalone mode', 'info');
                    return;
                }

                // Test if DOM elements exist
                const elements = [
                    'message-input',
                    'send-button',
                    'chat-display',
                    'chat-messages'
                ];

                let elementsFound = 0;
                elements.forEach(elementId => {
                    const element = window.parent.document.getElementById(elementId);
                    if (element) {
                        logResult(`✓ Element ${elementId} exists`, 'success');
                        elementsFound++;
                    } else {
                        logResult(`✗ Element ${elementId} missing`, 'error');
                    }
                });

                if (elementsFound === elements.length) {
                    logResult('✅ All message handling elements are available', 'success');
                } else {
                    logResult(`⚠️ ${elementsFound}/${elements.length} elements found`, 'error');
                }

                // Test addMessage function
                if (typeof window.parent.addMessage === 'function') {
                    try {
                        // Test adding a system message (should not save)
                        window.parent.addMessage('system', 'Test message from chat test', true);
                        logResult('✓ addMessage function works', 'success');
                    } catch (error) {
                        logResult(`✗ addMessage function error: ${error.message}`, 'error');
                    }
                } else {
                    logResult('✗ addMessage function not available', 'error');
                }

            } catch (error) {
                logResult(`✗ Message handling test failed: ${error.message}`, 'error');
            }
        }

        function testSessionManagement() {
            logResult('Testing Session Management...', 'info');
            
            try {
                if (!window.parent || window.parent === window) {
                    logResult('! Cannot test session management in standalone mode', 'info');
                    return;
                }

                // Test storage
                const STORAGE_KEY = 'ai_chat_sessions_v2';
                const hasStorage = localStorage.getItem(STORAGE_KEY) !== null;
                
                if (hasStorage) {
                    try {
                        const sessions = JSON.parse(localStorage.getItem(STORAGE_KEY));
                        logResult(`✓ Found ${sessions.length} sessions in storage`, 'success');
                    } catch (error) {
                        logResult('✗ Storage data is corrupted', 'error');
                    }
                } else {
                    logResult('! No sessions found in storage (expected for new installation)', 'info');
                }

                // Test session creation
                if (typeof window.parent.createNewChatSession === 'function') {
                    try {
                        const sessionId = window.parent.createNewChatSession();
                        if (sessionId) {
                            logResult('✓ New chat session created successfully', 'success');
                        } else {
                            logResult('✗ New chat session creation returned no ID', 'error');
                        }
                    } catch (error) {
                        logResult(`✗ Session creation error: ${error.message}`, 'error');
                    }
                } else {
                    logResult('✗ createNewChatSession function not available', 'error');
                }

                // Test current session state
                if (window.parent.currentChatSession) {
                    const session = window.parent.currentChatSession;
                    if (session.id && Array.isArray(session.messages)) {
                        logResult('✓ Current session has valid structure', 'success');
                    } else {
                        logResult('✗ Current session has invalid structure', 'error');
                    }
                } else {
                    logResult('✗ No current session available', 'error');
                }

            } catch (error) {
                logResult(`✗ Session management test failed: ${error.message}`, 'error');
            }
        }

        // Initialize on load
        window.addEventListener('load', () => {
            logResult('Quick Chat System Test Loaded', 'info');
            
            // Auto-run basic test
            setTimeout(() => {
                testChatSystemBasics();
            }, 500);
        });
    </script>
</body>
</html>
