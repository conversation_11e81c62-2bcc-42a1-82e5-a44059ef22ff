package com.jermescode.ai;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import fi.iki.elonen.NanoHTTPD;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Local HTTP server to serve web assets with proper CORS headers
 * This solves the CORS issue by serving content via HTTP instead of file:// protocol
 */
public class LocalWebServer extends NanoHTTPD {
    private static final String TAG = "LocalWebServer";
    private final Context context;
    private final AssetManager assetManager;

    public LocalWebServer(Context context, int port) {
        super(port);
        this.context = context;
        this.assetManager = context.getAssets();
    }

    @Override
    public Response serve(IHTTPSession session) {
        String uri = session.getUri();
        
        // Remove leading slash and default to index.html
        if (uri.equals("/") || uri.isEmpty()) {
            uri = "/index.html";
        }
        if (uri.startsWith("/")) {
            uri = uri.substring(1);
        }
        
        // Add www/ prefix for assets
        String assetPath = "www/" + uri;
        
        Log.d(TAG, "Serving: " + assetPath);
        
        try {
            // Try to open the asset
            InputStream inputStream = assetManager.open(assetPath);
            
            // Determine MIME type
            String mimeType = getMimeType(uri);
            
            // Create response with CORS headers
            Response response = newFixedLengthResponse(Response.Status.OK, mimeType, inputStream, getAssetSize(assetPath));
            
            // Add CORS headers to allow external API calls
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, Origin");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.addHeader("Pragma", "no-cache");
            response.addHeader("Expires", "0");
            
            return response;
            
        } catch (IOException e) {
            Log.e(TAG, "Asset not found: " + assetPath, e);
            return newFixedLengthResponse(Response.Status.NOT_FOUND, "text/plain", "File not found: " + uri);
        }
    }

    private String getMimeType(String fileName) {
        if (fileName.endsWith(".html") || fileName.endsWith(".htm")) {
            return "text/html; charset=utf-8";
        } else if (fileName.endsWith(".js")) {
            return "application/javascript; charset=utf-8";
        } else if (fileName.endsWith(".css")) {
            return "text/css; charset=utf-8";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (fileName.endsWith(".ico")) {
            return "image/x-icon";
        } else if (fileName.endsWith(".json")) {
            return "application/json; charset=utf-8";
        } else if (fileName.endsWith(".xml")) {
            return "application/xml; charset=utf-8";
        } else if (fileName.endsWith(".txt")) {
            return "text/plain; charset=utf-8";
        } else {
            return "application/octet-stream";
        }
    }

    private long getAssetSize(String assetPath) {
        try {
            InputStream inputStream = assetManager.open(assetPath);
            long size = inputStream.available();
            inputStream.close();
            return size;
        } catch (IOException e) {
            return -1; // Unknown size
        }
    }

    public void startServer() {
        try {
            start(NanoHTTPD.SOCKET_READ_TIMEOUT, false);
            Log.i(TAG, "Local web server started on port " + getListeningPort());
        } catch (IOException e) {
            Log.e(TAG, "Failed to start local web server", e);
        }
    }

    public void stopServer() {
        stop();
        Log.i(TAG, "Local web server stopped");
    }

    public String getServerUrl() {
        return "http://localhost:" + getListeningPort();
    }
}
