<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Fixes Test - JermesaCode Ai</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--chat-bg);
        }
        .test-button {
            margin: 5px;
            padding: 10px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: var(--primary-hover);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body class="dark-theme">
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <img src="icons/ai_chat_logo.svg" alt="AI Chat" class="logo-icon">
                    <span>Panel Fixes Test</span>
                </div>
            </div>
            <button id="new-chat-button" class="new-chat-button">
                + New Chat
            </button>
            <div class="history-section">
                <div class="history-header">
                    <span>History</span>
                    <button id="export-import-toggle" class="history-action-button" title="Export/Import Chat History">
                        <i class="fa-solid fa-download"></i>
                    </button>
                </div>
                <div id="chat-history" class="chat-history">
                    <div class="chat-item">
                        <img src="icons/history_chat_icon.svg" alt="Chat" style="width: 16px; height: 16px; margin-right: 8px;">
                        <span>Test Chat 1</span>
                    </div>
                    <div class="chat-item">
                        <img src="icons/history_chat_icon.svg" alt="Chat" style="width: 16px; height: 16px; margin-right: 8px;">
                        <span>Test Chat 2</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="chat-container">
            <div class="chat-window">
                <div class="chat-header">
                    <div class="header-actions">
                        <button id="reload-button" class="icon-button" title="Reload/Refresh Chat">
                            <img src="icons/refresh.svg" alt="Reload" class="icon">
                        </button>
                        <button id="theme-toggle" class="icon-button" title="Toggle dark/light mode">
                            <img src="icons/light_mode.svg" alt="Theme" class="icon">
                        </button>
                        <button id="ai-persona-toggle" class="icon-button" title="AI Persona Settings">
                            <i class="fa-solid fa-user-gear"></i>
                        </button>
                        <button id="attribution-toggle" class="icon-button" title="View Attributions & Credits">
                            <i class="fa-solid fa-info-circle"></i>
                        </button>
                        <button id="mobile-sidebar-toggle" class="mobile-sidebar-toggle">
                            <i class="fa-solid fa-bars"></i>
                        </button>
                    </div>
                </div>

                <div class="test-container">
                    <h1>Panel Fixes Test</h1>
                    
                    <div class="test-section">
                        <h3>🔧 Panel Management Tests</h3>
                        <p>Test the panel opening/closing behavior and ensure they don't stay on top when switching chats.</p>
                        
                        <button class="test-button" onclick="testOpenPersonaPanel()">Open AI Persona Panel</button>
                        <button class="test-button" onclick="testOpenExportPanel()">Open Export Panel</button>
                        <button class="test-button" onclick="testCloseAllPanels()">Close All Panels</button>
                        <button class="test-button" onclick="testNewChatCreation()">Create New Chat</button>
                        <button class="test-button" onclick="testChatSwitching()">Switch Chat</button>
                        
                        <div id="panel-status" class="status info">
                            Click buttons above to test panel behavior. Panels should close automatically when creating new chats or switching between chats.
                        </div>
                    </div>

                    <div class="test-section">
                        <h3>🔄 Reload Functionality Tests</h3>
                        <p>Test the improved reload functionality that should perform a hard reload/restart.</p>
                        
                        <button class="test-button" onclick="testReloadFunction()">Test Reload Function</button>
                        <button class="test-button" onclick="testAndroidRestart()">Test Android Restart (if available)</button>
                        
                        <div id="reload-status" class="status info">
                            Test reload functionality. In Android WebView, it should restart the app. In browsers, it should perform a hard reload.
                        </div>
                    </div>

                    <div class="test-section">
                        <h3>👆 Click Outside Tests</h3>
                        <p>Test clicking outside panels to ensure they close properly.</p>
                        
                        <button class="test-button" onclick="testClickOutside()">Open Panel & Test Click Outside</button>
                        
                        <div id="click-status" class="status info">
                            Open a panel and then click anywhere outside it. The panel should close automatically.
                        </div>
                    </div>

                    <div class="test-section">
                        <h3>📱 Mobile Specific Tests</h3>
                        <p>Test mobile-specific behavior and responsive design.</p>
                        
                        <button class="test-button" onclick="testMobileSidebar()">Toggle Mobile Sidebar</button>
                        <button class="test-button" onclick="testMobilePanels()">Test Mobile Panel Behavior</button>
                        
                        <div id="mobile-status" class="status info">
                            Test mobile-specific functionality. Panels should work properly on mobile devices.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Persona Settings Panel -->
    <div id="ai-persona-panel" class="settings-panel">
        <div class="settings-header">
            <h3>AI Persona Settings</h3>
            <button id="close-persona-panel" class="close-panel-button">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
        <div class="settings-content">
            <p>This is the AI Persona panel for testing. It should close when:</p>
            <ul>
                <li>Clicking outside the panel</li>
                <li>Creating a new chat</li>
                <li>Switching between chats</li>
                <li>Clicking the close button</li>
                <li>Reloading the app</li>
            </ul>
        </div>
    </div>

    <!-- Export/Import Panel -->
    <div id="export-import-panel" class="settings-panel">
        <div class="settings-header">
            <h3>Export/Import Chat History</h3>
            <button id="close-export-panel" class="close-panel-button">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
        <div class="settings-content">
            <p>This is the Export/Import panel for testing. It should close when:</p>
            <ul>
                <li>Clicking outside the panel</li>
                <li>Creating a new chat</li>
                <li>Switching between chats</li>
                <li>Clicking the close button</li>
                <li>Reloading the app</li>
            </ul>
        </div>
    </div>

    <!-- Attribution Panel -->
    <div id="attribution-panel" class="attribution-panel">
        <div class="attribution-content">
            <h3>Attribution Panel Test</h3>
            <p>This is the attribution panel for testing.</p>
            <button id="close-attribution" class="close-attribution-button">Close</button>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // Test functions
        function testOpenPersonaPanel() {
            const panel = document.getElementById('ai-persona-panel');
            panel.classList.add('open');
            updateStatus('panel-status', 'AI Persona panel opened. Try clicking outside to close it.', 'info');
        }

        function testOpenExportPanel() {
            const panel = document.getElementById('export-import-panel');
            panel.classList.add('open');
            updateStatus('panel-status', 'Export panel opened. Try clicking outside to close it.', 'info');
        }

        function testCloseAllPanels() {
            if (typeof closeAllPanels === 'function') {
                closeAllPanels();
                updateStatus('panel-status', 'closeAllPanels() function called successfully.', 'success');
            } else {
                updateStatus('panel-status', 'closeAllPanels() function not found!', 'error');
            }
        }

        function testNewChatCreation() {
            if (typeof createNewChatSession === 'function') {
                createNewChatSession();
                updateStatus('panel-status', 'New chat created. All panels should be closed.', 'success');
            } else {
                updateStatus('panel-status', 'createNewChatSession() function not found!', 'error');
            }
        }

        function testChatSwitching() {
            updateStatus('panel-status', 'Chat switching test - panels should close when switching chats.', 'info');
        }

        function testReloadFunction() {
            if (typeof reloadChat === 'function') {
                updateStatus('reload-status', 'Reload function will be called in 2 seconds...', 'info');
                setTimeout(() => {
                    reloadChat();
                }, 2000);
            } else {
                updateStatus('reload-status', 'reloadChat() function not found!', 'error');
            }
        }

        function testAndroidRestart() {
            if (typeof window.restartApp === 'function') {
                updateStatus('reload-status', 'Android restart function available. Testing...', 'info');
                if (window.restartApp()) {
                    updateStatus('reload-status', 'Android restart initiated successfully.', 'success');
                } else {
                    updateStatus('reload-status', 'Android restart failed or not available.', 'error');
                }
            } else {
                updateStatus('reload-status', 'Android restart function not available (normal for web browsers).', 'info');
            }
        }

        function testClickOutside() {
            testOpenPersonaPanel();
            updateStatus('click-status', 'Panel opened. Now click anywhere outside the panel to test auto-close.', 'info');
        }

        function testMobileSidebar() {
            if (typeof toggleSidebar === 'function') {
                toggleSidebar();
                updateStatus('mobile-status', 'Mobile sidebar toggled.', 'success');
            } else {
                updateStatus('mobile-status', 'toggleSidebar() function not found!', 'error');
            }
        }

        function testMobilePanels() {
            updateStatus('mobile-status', 'Testing mobile panel behavior...', 'info');
            testOpenPersonaPanel();
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // Initialize test environment
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel fixes test page loaded');
            
            // Test if main functions are available
            const functions = ['closeAllPanels', 'createNewChatSession', 'reloadChat', 'toggleSidebar'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    console.log(`✅ ${func} function is available`);
                } else {
                    console.log(`❌ ${func} function is NOT available`);
                }
            });
        });
    </script>
</body>
</html>
