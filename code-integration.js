// Smart Code Integration Module
class CodeIntegrationManager {
    constructor() {
        this.pendingChanges = [];
        this.appliedChanges = [];
        this.undoStack = [];
        this.redoStack = [];
        this.isAnalyzing = false;

        // Don't initialize event listeners in constructor - let main script handle it
    }
    
    initializeEventListeners() {
        console.log('Initializing Code Integration event listeners...');

        // Apply code button
        const applyCodeButton = document.getElementById('apply-code-button');
        if (applyCodeButton) {
            applyCodeButton.addEventListener('click', () => {
                console.log('Apply code button clicked');
                this.analyzeLastResponse();
            });
            console.log('Apply code button event listener added');
        } else {
            console.warn('Apply code button not found');
        }

        // Code integration panel controls
        const closeCodePanel = document.getElementById('close-code-panel');
        const previewChanges = document.getElementById('preview-changes');
        const applyChanges = document.getElementById('apply-changes');
        const cancelChanges = document.getElementById('cancel-changes');

        if (closeCodePanel) {
            closeCodePanel.addEventListener('click', () => {
                console.log('Close code panel clicked');
                this.hideCodePanel();
            });
            console.log('Close code panel event listener added');
        }

        if (previewChanges) {
            previewChanges.addEventListener('click', () => {
                console.log('Preview changes clicked');
                this.previewChanges();
            });
            console.log('Preview changes event listener added');
        }

        if (applyChanges) {
            applyChanges.addEventListener('click', () => {
                console.log('Apply changes clicked');
                this.applyChanges();
            });
            console.log('Apply changes event listener added');
        }

        if (cancelChanges) {
            cancelChanges.addEventListener('click', () => {
                console.log('Cancel changes clicked');
                this.hideCodePanel();
            });
            console.log('Cancel changes event listener added');
        }

        // Listen for new AI responses
        this.observeNewMessages();
        console.log('Code Integration event listeners initialized successfully');
    }
    
    observeNewMessages() {
        const chatDisplay = document.getElementById('chat-display');
        if (!chatDisplay) return;
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE && 
                            node.classList.contains('message-container')) {
                            const assistantMessage = node.querySelector('.assistant-message');
                            if (assistantMessage) {
                                this.analyzeMessageForCode(assistantMessage);
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe(chatDisplay, { childList: true, subtree: true });
    }
    
    analyzeMessageForCode(messageElement) {
        const messageText = messageElement.textContent || messageElement.innerText;

        // Look for code patterns that suggest file modifications
        const codePatterns = [
            /(?:create|modify|update|edit|change|replace|add|insert|delete|remove)\s+(?:file|the file|code|this code)\s+[`'""]?([^`'"\s]+)[`'""]?/gi,
            /(?:in|edit|modify|update|change)\s+[`'""]?([^`'"\s]+\.(?:js|py|html|css|json|md|txt|jsx|ts|tsx|vue|php|rb|go|rs|java|cpp|c|h))[`'""]?/gi,
            /(?:line\s+)(\d+)(?:\s*[-:]\s*(\d+))?/gi,
            /```[\w]*\n([\s\S]*?)\n```/g,
            /(?:here'?s|here is|this is|use this|try this|replace with|change to)\s+(?:the|your|some)?\s*(?:code|implementation|solution)/gi,
            /(?:function|class|method|variable|const|let|var|def|import|export)\s+\w+/gi
        ];

        let hasCodeSuggestions = false;

        // Check if the message contains code suggestions
        codePatterns.forEach(pattern => {
            const testPattern = new RegExp(pattern.source, pattern.flags);
            if (testPattern.test(messageText)) {
                hasCodeSuggestions = true;
            }
        });

        // Also check for code blocks specifically
        const codeBlocks = messageElement.querySelectorAll('pre code');
        if (codeBlocks.length > 0) {
            hasCodeSuggestions = true;
        }

        // Show apply code button if code suggestions are detected
        const applyCodeButton = document.getElementById('apply-code-button');
        if (applyCodeButton && hasCodeSuggestions) {
            applyCodeButton.style.display = 'flex';
            applyCodeButton.title = 'Apply AI code suggestions to editor';
            console.log('Code suggestions detected, showing apply button');
        }
    }
    
    analyzeLastResponse() {
        console.log('Analyzing last AI response for code changes...');
        const messages = document.querySelectorAll('.assistant-message');
        if (messages.length === 0) {
            console.log('No assistant messages found');
            return;
        }

        const lastMessage = messages[messages.length - 1];
        const messageText = lastMessage.textContent || lastMessage.innerText;

        this.isAnalyzing = true;
        this.showAnalyzingIndicator();

        // Extract code blocks and file references
        const codeBlocks = this.extractCodeBlocks(lastMessage);
        const fileReferences = this.extractFileReferences(messageText);

        console.log(`Found ${codeBlocks.length} code blocks and ${fileReferences.length} file references`);

        if (codeBlocks.length > 0 || fileReferences.length > 0) {
            this.pendingChanges = this.createChangeSet(codeBlocks, fileReferences, messageText);
            console.log('Created change set with', this.pendingChanges.length, 'changes');
            this.showCodePanel();
        } else {
            this.showNotification('No code changes detected in the AI response. Try asking for specific code examples or file modifications.');
        }

        this.isAnalyzing = false;
        this.hideAnalyzingIndicator();
    }
    
    extractCodeBlocks(messageElement) {
        const codeBlocks = [];
        const preElements = messageElement.querySelectorAll('pre code');

        preElements.forEach((codeElement, index) => {
            const code = codeElement.textContent;
            const language = this.detectLanguage(codeElement);
            const filename = this.inferFilename(code, language, index);

            // Only include code blocks that have substantial content
            if (code.trim().length > 10) {
                codeBlocks.push({
                    id: `block_${index}`,
                    code: code,
                    language: language,
                    filename: filename,
                    type: 'code_block'
                });
            }
        });

        return codeBlocks;
    }
    
    extractFileReferences(messageText) {
        const fileReferences = [];
        const patterns = [
            /(?:create|modify|update|edit)\s+(?:file|the file)\s+[`'""]?([^`'"\s]+)[`'""]?/gi,
            /(?:in|edit|modify)\s+[`'""]?([^`'"\s]+\.(?:js|py|html|css|json|md|txt))[`'""]?/gi
        ];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(messageText)) !== null) {
                const filename = match[1];
                if (!fileReferences.find(ref => ref.filename === filename)) {
                    fileReferences.push({
                        filename: filename,
                        type: 'file_reference'
                    });
                }
            }
        });
        
        return fileReferences;
    }
    
    detectLanguage(codeElement) {
        const classNames = codeElement.className.split(' ');
        for (const className of classNames) {
            if (className.startsWith('language-')) {
                return className.replace('language-', '');
            }
        }

        // Try to detect language from code content if no class is found
        const code = codeElement.textContent;
        if (code.includes('function ') || code.includes('const ') || code.includes('let ') || code.includes('var ')) {
            return 'javascript';
        }
        if (code.includes('def ') || code.includes('import ') || code.includes('from ')) {
            return 'python';
        }
        if (code.includes('<!DOCTYPE') || code.includes('<html') || code.includes('<div')) {
            return 'html';
        }
        if (code.includes('{') && (code.includes('color:') || code.includes('margin:') || code.includes('padding:'))) {
            return 'css';
        }

        return 'text';
    }
    
    inferFilename(code, language, index) {
        // Try to infer filename from code content
        const patterns = {
            'javascript': /\/\*\s*@file\s+([^\s*]+)\s*\*\/|\/\/\s*@file\s+([^\s\n]+)/,
            'python': /#\s*@file\s+([^\s\n]+)/,
            'html': /<!--\s*@file\s+([^\s-]+)\s*-->/,
            'css': /\/\*\s*@file\s+([^\s*]+)\s*\*\//
        };
        
        const pattern = patterns[language];
        if (pattern) {
            const match = code.match(pattern);
            if (match) {
                return match[1] || match[2];
            }
        }
        
        // Default filename based on language
        const extensions = {
            'javascript': 'js',
            'python': 'py',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'markdown': 'md'
        };
        
        const ext = extensions[language] || 'txt';
        return `suggested_${index + 1}.${ext}`;
    }
    
    createChangeSet(codeBlocks, fileReferences, messageText) {
        const changes = [];
        
        // Process code blocks
        codeBlocks.forEach(block => {
            changes.push({
                id: block.id,
                type: 'code_replacement',
                filename: block.filename,
                language: block.language,
                newCode: block.code,
                description: `Replace content in ${block.filename}`
            });
        });
        
        // Process file references
        fileReferences.forEach(ref => {
            if (!changes.find(change => change.filename === ref.filename)) {
                changes.push({
                    id: `ref_${ref.filename}`,
                    type: 'file_modification',
                    filename: ref.filename,
                    description: `Modify ${ref.filename} as described in the AI response`
                });
            }
        });
        
        return changes;
    }
    
    showCodePanel() {
        const panel = document.getElementById('code-integration-panel');
        if (panel) {
            panel.style.display = 'flex';
            this.renderChanges();
        }
    }
    
    hideCodePanel() {
        const panel = document.getElementById('code-integration-panel');
        if (panel) {
            panel.style.display = 'none';
        }
        
        // Hide apply code button
        const applyCodeButton = document.getElementById('apply-code-button');
        if (applyCodeButton) {
            applyCodeButton.style.display = 'none';
        }
    }
    
    renderChanges() {
        const fileTabs = document.getElementById('file-tabs');
        const diffViewer = document.getElementById('diff-viewer');
        
        if (!fileTabs || !diffViewer) return;
        
        // Clear existing content
        fileTabs.innerHTML = '';
        diffViewer.innerHTML = '';
        
        if (this.pendingChanges.length === 0) {
            diffViewer.innerHTML = '<p>No changes to preview.</p>';
            return;
        }
        
        // Create file tabs
        this.pendingChanges.forEach((change, index) => {
            const tab = document.createElement('button');
            tab.className = `file-tab ${index === 0 ? 'active' : ''}`;
            tab.textContent = change.filename;
            tab.addEventListener('click', () => {
                this.showChangePreview(change, tab);
            });
            fileTabs.appendChild(tab);
        });
        
        // Show first change by default
        if (this.pendingChanges.length > 0) {
            this.showChangePreview(this.pendingChanges[0]);
        }
    }
    
    showChangePreview(change, activeTab = null) {
        const diffViewer = document.getElementById('diff-viewer');
        if (!diffViewer) return;
        
        // Update active tab
        if (activeTab) {
            document.querySelectorAll('.file-tab').forEach(tab => tab.classList.remove('active'));
            activeTab.classList.add('active');
        }
        
        // Show change preview
        let content = '';
        
        if (change.type === 'code_replacement') {
            content = `
                <div class="change-header">
                    <h4>${change.filename}</h4>
                    <p>${change.description}</p>
                </div>
                <div class="code-preview">
                    <pre><code class="language-${change.language}">${this.escapeHtml(change.newCode)}</code></pre>
                </div>
            `;
        } else {
            content = `
                <div class="change-header">
                    <h4>${change.filename}</h4>
                    <p>${change.description}</p>
                </div>
                <div class="change-description">
                    <p>This file will be modified according to the AI's suggestions. Please review the AI response for specific changes.</p>
                </div>
            `;
        }
        
        diffViewer.innerHTML = content;
        
        // Apply syntax highlighting
        if (window.hljs) {
            diffViewer.querySelectorAll('pre code').forEach(block => {
                hljs.highlightElement(block);
            });
        }
    }
    
    previewChanges() {
        this.showNotification('Preview functionality will be implemented in the next update.');
    }
    
    applyChanges() {
        console.log('Applying code changes...');
        if (this.pendingChanges.length === 0) {
            console.log('No pending changes to apply');
            return;
        }

        // Check if code editor is available
        if (!window.codeEditor) {
            console.log('Code editor not available');
            this.showNotification('Code editor not available. Please open the code editor first by clicking the code editor button.');
            return;
        }

        const firstChange = this.pendingChanges[0];
        console.log('Applying first change:', firstChange);

        if (firstChange.type === 'code_replacement') {
            try {
                // Save current state for undo
                this.undoStack.push({
                    content: window.codeEditor.getValue(),
                    timestamp: Date.now()
                });

                // Apply the change
                window.codeEditor.setValue(firstChange.newCode);

                // Update language if needed
                const languageSelect = document.getElementById('language-select');
                if (languageSelect && firstChange.language) {
                    const languageMap = {
                        'javascript': 'javascript',
                        'js': 'javascript',
                        'python': 'python',
                        'py': 'python',
                        'html': 'htmlmixed',
                        'htmlmixed': 'htmlmixed',
                        'css': 'css',
                        'json': 'javascript',
                        'typescript': 'javascript',
                        'ts': 'javascript',
                        'jsx': 'javascript',
                        'tsx': 'javascript',
                        'text': 'text'
                    };

                    const editorLanguage = languageMap[firstChange.language.toLowerCase()] || firstChange.language;
                    console.log(`Changing editor language to: ${editorLanguage}`);
                    languageSelect.value = editorLanguage;
                    if (window.changeEditorLanguage) {
                        window.changeEditorLanguage(editorLanguage);
                    }
                }

                this.appliedChanges.push(...this.pendingChanges);
                this.pendingChanges = [];

                this.showNotification(`✅ Successfully applied code changes to editor: ${firstChange.filename}`);
                this.hideCodePanel();
                console.log('Code changes applied successfully');
            } catch (error) {
                console.error('Error applying code changes:', error);
                this.showNotification('❌ Error applying code changes. Please try again.');
            }
        } else {
            this.showNotification('This change type is not yet supported for automatic application.');
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showAnalyzingIndicator() {
        const applyCodeButton = document.getElementById('apply-code-button');
        if (applyCodeButton) {
            applyCodeButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i>';
            applyCodeButton.disabled = true;
        }
    }
    
    hideAnalyzingIndicator() {
        const applyCodeButton = document.getElementById('apply-code-button');
        if (applyCodeButton) {
            applyCodeButton.innerHTML = '<i class="fa-solid fa-magic-wand-sparkles"></i>';
            applyCodeButton.disabled = false;
        }
    }
    
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: var(--shadow-lg);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the Code Integration Manager
let codeIntegrationManager;

// Don't auto-initialize - let the main script handle it
// This prevents conflicts with event listeners

function initializeCodeIntegrationManager() {
    if (!codeIntegrationManager) {
        codeIntegrationManager = new CodeIntegrationManager();
        window.codeIntegrationManager = codeIntegrationManager; // Make it globally available
        console.log('Code Integration Manager initialized');
    }
    return codeIntegrationManager;
}

// Expose the initialization function globally
window.initializeCodeIntegrationManager = initializeCodeIntegrationManager;
