#!/bin/bash

echo "========================================"
echo "JermesaCode Ai Android - Test and Build"
echo "========================================"
echo

echo "📋 Checking project structure..."
if [ ! -f "app/src/main/assets/www/index.html" ]; then
    echo "❌ Web assets not found!"
    echo "Please ensure all web files are copied to app/src/main/assets/www/"
    exit 1
fi

if [ ! -f "app/src/main/assets/www/android-bridge.js" ]; then
    echo "❌ Android bridge not found!"
    echo "The android-bridge.js file is required for API connectivity."
    exit 1
fi

echo "✅ Project structure looks good"

echo
echo "🧹 Cleaning previous builds..."
./gradlew clean

echo
echo "🔨 Building debug APK..."
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo
    echo "✅ Build successful!"
    echo
    echo "📱 APK Location: app/build/outputs/apk/debug/app-debug.apk"
    echo
    echo "🚀 To install on device:"
    echo "   1. Enable USB Debugging on your Android device"
    echo "   2. Connect device via USB"
    echo "   3. Run: ./gradlew installDebug"
    echo
    echo "🔧 To test API connectivity:"
    echo "   1. Install and open the app"
    echo "   2. Set up an AI provider (OpenAI, Gemini, etc.)"
    echo "   3. Try sending a message"
    echo "   4. Check Android logs: adb logcat | grep JermesaCode"
    echo
    echo "📝 Troubleshooting:"
    echo "   - If API calls fail, check internet connection"
    echo "   - Verify API keys are correct"
    echo "   - Check Android logs for detailed error messages"
    echo "   - Ensure the provider URL is accessible"
else
    echo
    echo "❌ Build failed!"
    echo "Check the error messages above for details."
    echo
    echo "Common issues:"
    echo "- Missing Android SDK"
    echo "- Incorrect ANDROID_HOME environment variable"
    echo "- Missing Java JDK"
    echo "- Network connectivity issues"
fi

echo
