/**
 * Android Bridge for API Calls
 * This file provides a bridge between the web application and Android native code
 * to bypass CORS restrictions when making API calls to external AI providers.
 */

(function() {
    'use strict';

    // Check if we're running in Android WebView
    const isAndroid = typeof AndroidAPI !== 'undefined';
    
    if (!isAndroid) {
        console.log('Android bridge not available, using standard fetch');
        return;
    }

    console.log('Android bridge initialized');

    // Store original fetch function
    const originalFetch = window.fetch;

    // Callback storage for async operations
    window.apiCallbacks = {};
    window.streamCallbacks = {};

    // Generate unique callback ID
    function generateCallbackId() {
        return 'callback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Check if URL should be routed through Android
    function shouldUseAndroidBridge(url) {
        // Route external API calls through Android
        const externalDomains = [
            'api.openai.com',
            'generativelanguage.googleapis.com',
            'api.deepseek.com',
            'openrouter.ai',
            'localhost:11434', // Ollama
            '127.0.0.1:11434'  // Ollama
        ];
        
        return externalDomains.some(domain => url.includes(domain));
    }

    // Enhanced fetch function that routes through Android when needed
    window.fetch = function(url, options = {}) {
        // Use original fetch for local resources
        if (!shouldUseAndroidBridge(url)) {
            return originalFetch(url, options);
        }

        console.log('Routing API call through Android bridge:', url);

        return new Promise((resolve, reject) => {
            const callbackId = generateCallbackId();
            
            // Prepare request data
            const method = options.method || 'GET';
            const headers = JSON.stringify(options.headers || {});
            const body = options.body || '';

            // Store callback
            window.apiCallbacks[callbackId] = function(result) {
                try {
                    if (result.success) {
                        // Create a Response-like object
                        const response = {
                            ok: result.status >= 200 && result.status < 300,
                            status: result.status,
                            statusText: result.status >= 200 && result.status < 300 ? 'OK' : 'Error',
                            headers: new Headers(),
                            url: url,
                            json: function() {
                                return Promise.resolve(JSON.parse(result.data));
                            },
                            text: function() {
                                return Promise.resolve(result.data);
                            },
                            body: result.data
                        };

                        // Handle streaming responses
                        if (options.signal && options.signal.addEventListener) {
                            options.signal.addEventListener('abort', () => {
                                reject(new Error('Request aborted'));
                            });
                        }

                        resolve(response);
                    } else {
                        reject(new Error(result.error || 'API call failed'));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            // Make the call through Android
            try {
                AndroidAPI.makeApiCall(url, method, headers, body, callbackId);
            } catch (error) {
                delete window.apiCallbacks[callbackId];
                reject(error);
            }
        });
    };

    // Enhanced streaming fetch for Server-Sent Events
    window.fetchStream = function(url, options = {}, onChunk) {
        if (!shouldUseAndroidBridge(url)) {
            // Fallback to original fetch for non-external URLs
            return originalFetch(url, options);
        }

        console.log('Routing streaming API call through Android bridge:', url);

        return new Promise((resolve, reject) => {
            const callbackId = generateCallbackId();
            
            // Prepare request data
            const method = options.method || 'GET';
            const headers = JSON.stringify(options.headers || {});
            const body = options.body || '';

            // Store streaming callback
            window.streamCallbacks[callbackId] = function(result) {
                try {
                    if (result.success) {
                        if (result.done) {
                            // Stream completed
                            resolve();
                        } else if (result.chunk && onChunk) {
                            // Process chunk
                            onChunk(result.chunk);
                        }
                    } else {
                        reject(new Error(result.error || 'Streaming API call failed'));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            // Make the streaming call through Android
            try {
                AndroidAPI.makeStreamingApiCall(url, method, headers, body, callbackId);
            } catch (error) {
                delete window.streamCallbacks[callbackId];
                reject(error);
            }
        });
    };

    // Override XMLHttpRequest for compatibility
    const OriginalXMLHttpRequest = window.XMLHttpRequest;
    
    window.XMLHttpRequest = function() {
        const xhr = new OriginalXMLHttpRequest();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        let requestUrl = '';
        let requestMethod = '';
        let requestHeaders = {};
        
        xhr.open = function(method, url, async, user, password) {
            requestUrl = url;
            requestMethod = method;
            
            if (shouldUseAndroidBridge(url)) {
                // Don't call original open for external URLs
                return;
            }
            
            return originalOpen.call(this, method, url, async, user, password);
        };
        
        xhr.setRequestHeader = function(name, value) {
            requestHeaders[name] = value;
            
            if (!shouldUseAndroidBridge(requestUrl)) {
                return OriginalXMLHttpRequest.prototype.setRequestHeader.call(this, name, value);
            }
        };
        
        xhr.send = function(data) {
            if (shouldUseAndroidBridge(requestUrl)) {
                // Route through Android bridge
                const callbackId = generateCallbackId();
                
                window.apiCallbacks[callbackId] = (result) => {
                    if (result.success) {
                        Object.defineProperty(xhr, 'status', { value: result.status });
                        Object.defineProperty(xhr, 'responseText', { value: result.data });
                        Object.defineProperty(xhr, 'readyState', { value: 4 });
                        
                        if (xhr.onreadystatechange) {
                            xhr.onreadystatechange();
                        }
                        if (xhr.onload) {
                            xhr.onload();
                        }
                    } else {
                        if (xhr.onerror) {
                            xhr.onerror(new Error(result.error));
                        }
                    }
                };
                
                AndroidAPI.makeApiCall(requestUrl, requestMethod, JSON.stringify(requestHeaders), data || '', callbackId);
                return;
            }
            
            return originalSend.call(this, data);
        };
        
        return xhr;
    };

    // Copy static properties
    Object.setPrototypeOf(window.XMLHttpRequest, OriginalXMLHttpRequest);
    Object.setPrototypeOf(window.XMLHttpRequest.prototype, OriginalXMLHttpRequest.prototype);

    console.log('Android bridge setup complete');
})();
