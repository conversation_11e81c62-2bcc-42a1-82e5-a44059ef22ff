// Export/Import Chat History Module
class ExportImportManager {
    constructor() {
        this.selectedChats = new Set();
        this.setupDownloadCallback();
        // Don't initialize event listeners in constructor - let main script handle it
    }

    setupDownloadCallback() {
        // Set up callback for Android file download completion
        window.onFileDownloadComplete = (success, message) => {
            if (success) {
                this.showNotification(`File saved successfully: ${message}`);
            } else {
                this.showNotification(`Download failed: ${message}`);
            }
        };
    }
    
    initializeEventListeners() {
        // Add a small delay to ensure DOM elements are available
        setTimeout(() => {
            this.setupEventListeners();
        }, 100);
    }

    setupEventListeners() {
        // Panel toggle
        const exportImportToggle = document.getElementById('export-import-toggle');
        const exportImportPanel = document.getElementById('export-import-panel');
        const closeExportPanel = document.getElementById('close-export-panel');

        console.log('Setting up export-import event listeners:', { exportImportToggle, exportImportPanel, closeExportPanel });

        if (exportImportToggle && exportImportPanel) {
            exportImportToggle.addEventListener('click', () => {
                console.log('Export-import toggle clicked');
                exportImportPanel.classList.toggle('open');
                if (exportImportPanel.classList.contains('open')) {
                    this.loadChatSelection();
                    this.setDefaultDateRange();
                }
            });
        }

        if (closeExportPanel && exportImportPanel) {
            closeExportPanel.addEventListener('click', () => {
                exportImportPanel.classList.remove('open');
            });
        }
        
        // Export controls
        const exportSelected = document.getElementById('export-selected');
        const exportAll = document.getElementById('export-all');
        const importChats = document.getElementById('import-chats');
        
        if (exportSelected) {
            exportSelected.addEventListener('click', () => {
                this.exportSelectedChats();
            });
        }
        
        if (exportAll) {
            exportAll.addEventListener('click', () => {
                this.exportAllChats();
            });
        }
        
        if (importChats) {
            importChats.addEventListener('click', () => {
                this.importChats();
            });
        }
    }
    
    setDefaultDateRange() {
        const startDateInput = document.getElementById('export-start-date');
        const endDateInput = document.getElementById('export-end-date');
        
        if (startDateInput && endDateInput) {
            const now = new Date();
            const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            
            startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];
            endDateInput.value = now.toISOString().split('T')[0];
        }
    }
    
    loadChatSelection() {
        const chatSelection = document.getElementById('chat-selection');
        if (!chatSelection) return;
        
        chatSelection.innerHTML = '';
        
        // NEW CHAT HISTORY SYSTEM - Get sessions from new storage
        const savedSessions = localStorage.getItem('ai_chat_sessions_v2');
        if (!savedSessions) {
            chatSelection.innerHTML = '<p>No chat history found.</p>';
            return;
        }

        try {
            const sessions = JSON.parse(savedSessions);
            const sortedSessions = sessions.sort((a, b) => b.timestamp - a.timestamp);

            if (sortedSessions.length === 0) {
                chatSelection.innerHTML = '<p>No chat history found.</p>';
                return;
            }

            sortedSessions.forEach(session => {
                const chatItem = document.createElement('div');
                chatItem.className = 'chat-selection-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.value = session.id;
                checkbox.id = `chat-${session.id}`;
                checkbox.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        this.selectedChats.add(session.id);
                    } else {
                        this.selectedChats.delete(session.id);
                    }
                });

                const label = document.createElement('label');
                label.htmlFor = `chat-${session.id}`;
                label.style.flex = '1';
                label.style.cursor = 'pointer';

                const title = document.createElement('div');
                title.textContent = session.title || 'Untitled Chat';
                title.style.fontWeight = '500';

                const details = document.createElement('div');
                details.style.fontSize = '0.8rem';
                details.style.color = 'var(--text-secondary)';
                details.textContent = `${new Date(session.timestamp).toLocaleDateString()} • ${session.messages?.length || 0} messages`;
                
                label.appendChild(title);
                label.appendChild(details);
                
                chatItem.appendChild(checkbox);
                chatItem.appendChild(label);
                chatSelection.appendChild(chatItem);
            });
            
            // Add select all/none buttons
            const selectControls = document.createElement('div');
            selectControls.style.cssText = 'display: flex; gap: 8px; margin-top: 12px; padding-top: 12px; border-top: 1px solid var(--border-color);';
            
            const selectAllBtn = document.createElement('button');
            selectAllBtn.textContent = 'Select All';
            selectAllBtn.className = 'action-button secondary';
            selectAllBtn.style.fontSize = '0.8rem';
            selectAllBtn.style.padding = '4px 8px';
            selectAllBtn.addEventListener('click', () => {
                const checkboxes = chatSelection.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    cb.checked = true;
                    this.selectedChats.add(cb.value);
                });
            });
            
            const selectNoneBtn = document.createElement('button');
            selectNoneBtn.textContent = 'Select None';
            selectNoneBtn.className = 'action-button secondary';
            selectNoneBtn.style.fontSize = '0.8rem';
            selectNoneBtn.style.padding = '4px 8px';
            selectNoneBtn.addEventListener('click', () => {
                const checkboxes = chatSelection.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    cb.checked = false;
                });
                this.selectedChats.clear();
            });
            
            selectControls.appendChild(selectAllBtn);
            selectControls.appendChild(selectNoneBtn);
            chatSelection.appendChild(selectControls);
            
        } catch (error) {
            console.error('Error loading chat selection:', error);
            chatSelection.innerHTML = '<p>Error loading chat history.</p>';
        }
    }
    
    getDateRange() {
        const startDateInput = document.getElementById('export-start-date');
        const endDateInput = document.getElementById('export-end-date');
        
        let startDate = null;
        let endDate = null;
        
        if (startDateInput && startDateInput.value) {
            startDate = new Date(startDateInput.value);
            startDate.setHours(0, 0, 0, 0);
        }
        
        if (endDateInput && endDateInput.value) {
            endDate = new Date(endDateInput.value);
            endDate.setHours(23, 59, 59, 999);
        }
        
        return { startDate, endDate };
    }
    
    filterChatsByDate(chats, startDate, endDate) {
        if (!startDate && !endDate) return chats;
        
        return chats.filter(chat => {
            const chatDate = new Date(chat.timestamp);
            
            if (startDate && chatDate < startDate) return false;
            if (endDate && chatDate > endDate) return false;
            
            return true;
        });
    }
    
    exportSelectedChats() {
        if (this.selectedChats.size === 0) {
            this.showNotification('Please select at least one chat to export.');
            return;
        }

        // NEW CHAT HISTORY SYSTEM - Export from new storage
        const savedSessions = localStorage.getItem('ai_chat_sessions_v2');
        if (!savedSessions) {
            this.showNotification('No chat history found.');
            return;
        }

        try {
            const allSessions = JSON.parse(savedSessions);
            const selectedSessionData = Array.from(this.selectedChats)
                .map(id => allSessions.find(session => session.id === id))
                .filter(Boolean);

            const { startDate, endDate } = this.getDateRange();
            const filteredSessions = this.filterChatsByDate(selectedSessionData, startDate, endDate);

            if (filteredSessions.length === 0) {
                this.showNotification('No chats found in the selected date range.');
                return;
            }

            this.performExport(filteredSessions, 'selected_chats');
        } catch (error) {
            console.error('Error exporting selected chats:', error);
            this.showNotification('Error exporting chats.');
        }
    }
    
    exportAllChats() {
        // NEW CHAT HISTORY SYSTEM - Export from new storage
        const savedSessions = localStorage.getItem('ai_chat_sessions_v2');
        if (!savedSessions) {
            this.showNotification('No chat history found.');
            return;
        }

        try {
            const allSessions = JSON.parse(savedSessions);

            const { startDate, endDate } = this.getDateRange();
            const filteredSessions = this.filterChatsByDate(allSessions, startDate, endDate);

            if (filteredSessions.length === 0) {
                this.showNotification('No chats found in the selected date range.');
                return;
            }

            this.performExport(filteredSessions, 'all_chats');
        } catch (error) {
            console.error('Error exporting all chats:', error);
            this.showNotification('Error exporting chats.');
        }
    }
    
    performExport(chats, filename) {
        const formatSelect = document.getElementById('export-format');
        const format = formatSelect ? formatSelect.value : 'json';
        
        let content = '';
        let mimeType = '';
        let fileExtension = '';
        
        switch (format) {
            case 'json':
                content = JSON.stringify({
                    exportDate: new Date().toISOString(),
                    chatCount: chats.length,
                    chats: chats
                }, null, 2);
                mimeType = 'application/json';
                fileExtension = 'json';
                break;
                
            case 'markdown':
                content = this.convertToMarkdown(chats);
                mimeType = 'text/markdown';
                fileExtension = 'md';
                break;
                
            case 'text':
                content = this.convertToText(chats);
                mimeType = 'text/plain';
                fileExtension = 'txt';
                break;
                
            default:
                this.showNotification('Invalid export format selected.');
                return;
        }
        
        this.downloadFile(content, `${filename}_${new Date().toISOString().split('T')[0]}.${fileExtension}`, mimeType);
        this.showNotification(`Exported ${chats.length} chat(s) as ${format.toUpperCase()}.`);
    }
    
    convertToMarkdown(chats) {
        let markdown = `# Chat History Export\n\n`;
        markdown += `**Export Date:** ${new Date().toLocaleString()}\n`;
        markdown += `**Total Chats:** ${chats.length}\n\n`;
        markdown += `---\n\n`;
        
        chats.sort((a, b) => b.timestamp - a.timestamp).forEach((chat, index) => {
            markdown += `## Chat ${index + 1}: ${chat.title || 'Untitled'}\n\n`;
            markdown += `**Date:** ${new Date(chat.timestamp).toLocaleString()}\n`;
            markdown += `**Model:** ${chat.model || 'Unknown'}\n`;
            markdown += `**Messages:** ${chat.messages?.length || chat.history?.length || 0}\n\n`;
            
            // NEW CHAT HISTORY SYSTEM - Use messages instead of history
            if (chat.messages && chat.messages.length > 0) {
                chat.messages.forEach((message, msgIndex) => {
                    if (message.role !== 'system') {
                        const role = message.role === 'user' ? '👤 User' : '🤖 Assistant';
                        markdown += `### ${role}\n\n`;
                        markdown += `${message.content}\n\n`;
                    }
                });
            } else if (chat.history && chat.history.length > 0) {
                // Fallback for old format
                chat.history.forEach((message, msgIndex) => {
                    if (message.role !== 'system') {
                        const role = message.role === 'user' ? '👤 User' : '🤖 Assistant';
                        markdown += `### ${role}\n\n`;
                        markdown += `${message.content}\n\n`;
                    }
                });
            }
            
            markdown += `---\n\n`;
        });
        
        return markdown;
    }
    
    convertToText(chats) {
        let text = `CHAT HISTORY EXPORT\n`;
        text += `Export Date: ${new Date().toLocaleString()}\n`;
        text += `Total Chats: ${chats.length}\n\n`;
        text += `${'='.repeat(50)}\n\n`;
        
        chats.sort((a, b) => b.timestamp - a.timestamp).forEach((chat, index) => {
            text += `CHAT ${index + 1}: ${chat.title || 'Untitled'}\n`;
            text += `Date: ${new Date(chat.timestamp).toLocaleString()}\n`;
            text += `Model: ${chat.model || 'Unknown'}\n`;
            text += `Messages: ${chat.messages?.length || chat.history?.length || 0}\n\n`;

            // NEW CHAT HISTORY SYSTEM - Use messages instead of history
            if (chat.messages && chat.messages.length > 0) {
                chat.messages.forEach((message, msgIndex) => {
                    if (message.role !== 'system') {
                        const role = message.role === 'user' ? 'USER' : 'ASSISTANT';
                        text += `[${role}]\n${message.content}\n\n`;
                    }
                });
            } else if (chat.history && chat.history.length > 0) {
                // Fallback for old format
                chat.history.forEach((message, msgIndex) => {
                    if (message.role !== 'system') {
                        const role = message.role === 'user' ? 'USER' : 'ASSISTANT';
                        text += `[${role}]\n${message.content}\n\n`;
                    }
                });
            }
            
            text += `${'-'.repeat(50)}\n\n`;
        });
        
        return text;
    }
    
    downloadFile(content, filename, mimeType) {
        // Check if we're running in Android WebView
        if (typeof AndroidAPI !== 'undefined' && AndroidAPI.downloadFile) {
            // Use Android native download for WebView
            try {
                AndroidAPI.downloadFile(content, filename, mimeType);
                return;
            } catch (error) {
                console.error('Android download failed, falling back to blob download:', error);
            }
        }

        // Fallback to blob download for web browsers
        try {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('File download failed:', error);
            this.showNotification('Failed to download file. Please try again.');
        }
    }
    
    importChats() {
        const fileInput = document.getElementById('import-file');
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            this.showNotification('Please select a file to import.');
            return;
        }
        
        const file = fileInput.files[0];
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                const content = e.target.result;
                let importedData = null;
                
                if (file.name.endsWith('.json')) {
                    importedData = JSON.parse(content);
                } else {
                    this.showNotification('Only JSON files are supported for import.');
                    return;
                }
                
                if (importedData && importedData.chats && Array.isArray(importedData.chats)) {
                    this.processImportedChats(importedData.chats);
                } else {
                    this.showNotification('Invalid file format. Expected exported chat data.');
                }
            } catch (error) {
                console.error('Error importing chats:', error);
                this.showNotification('Error reading the import file.');
            }
        };
        
        reader.readAsText(file);
    }
    
    processImportedChats(importedChats) {
        // NEW CHAT HISTORY SYSTEM - Import to new storage format
        const savedSessions = localStorage.getItem('ai_chat_sessions_v2');
        let existingSessions = [];

        if (savedSessions) {
            try {
                existingSessions = JSON.parse(savedSessions);
            } catch (error) {
                console.error('Error parsing existing sessions:', error);
            }
        }

        let importedCount = 0;
        let skippedCount = 0;

        importedChats.forEach(chat => {
            if (chat.id && !existingSessions.find(session => session.id === chat.id)) {
                // Convert old format to new format
                const processedSession = {
                    id: chat.id,
                    title: chat.title || 'Imported Chat',
                    messages: chat.messages || chat.history || [], // Support both formats
                    model: chat.model || '',
                    timestamp: chat.timestamp || Date.now()
                };
                existingSessions.push(processedSession);
                importedCount++;
            } else {
                skippedCount++;
            }
        });

        // Save to localStorage
        localStorage.setItem('ai_chat_sessions_v2', JSON.stringify(existingSessions));

        // Update the global sessions map immediately to ensure UI consistency
        if (window.allChatSessions !== undefined) {
            window.allChatSessions.clear();
            existingSessions.forEach(session => {
                window.allChatSessions.set(session.id, session);
            });
            console.log('Global chat sessions updated with imported chats');
        }

        // Force refresh the chat history display immediately
        if (window.renderChatSessionsList) {
            window.renderChatSessionsList();
            console.log('Chat sessions list refreshed after import');
        }

        // Also refresh the chat selection in the export panel if it's open
        setTimeout(() => {
            this.loadChatSelection();
        }, 200);

        this.showNotification(`Import completed: ${importedCount} chats imported, ${skippedCount} skipped (duplicates).`);

        // Clear the file input
        const fileInput = document.getElementById('import-file');
        if (fileInput) {
            fileInput.value = '';
        }

        console.log(`Import process completed: ${importedCount} imported, ${skippedCount} skipped`);
    }
    
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: var(--shadow-lg);
            max-width: 300px;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 4000);
    }
}

// Initialize the Export/Import Manager
let exportImportManager;

// Don't auto-initialize - let the main script handle it
// This prevents conflicts with event listeners

function initializeExportImportManager() {
    if (!exportImportManager) {
        exportImportManager = new ExportImportManager();
        window.exportImportManager = exportImportManager; // Make it globally available
        console.log('Export/Import Manager initialized');
    }
    return exportImportManager;
}

// Expose the initialization function globally
window.initializeExportImportManager = initializeExportImportManager;
