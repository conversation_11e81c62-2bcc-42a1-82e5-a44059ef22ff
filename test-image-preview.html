<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Preview Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .file-preview {
            padding: 16px;
            margin-bottom: 16px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .preview-item {
            margin-bottom: 16px;
            padding: 16px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .preview-item[data-clipboard="true"] {
            border-left: 3px solid #007bff;
        }
        
        .file-content {
            margin-top: 8px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow: auto;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 4px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .remove-preview {
            background: #dc3545;
        }
        
        .remove-preview:hover {
            background: #c82333;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        .paste-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .paste-area:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>🖼️ Image Preview Test</h1>
    
    <div class="test-section">
        <h2>📋 Clipboard Paste Test</h2>
        <div class="paste-area" id="paste-area">
            <p>📋 Copy an image and paste it here (Ctrl+V)</p>
            <p><small>Or click here and paste</small></p>
        </div>
        <div id="paste-result"></div>
    </div>
    
    <div class="test-section">
        <h2>📁 File Upload Test</h2>
        <input type="file" id="file-input" accept="image/*" multiple>
        <div id="upload-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🖼️ Preview Area</h2>
        <div class="file-preview" id="file-preview"></div>
    </div>

    <script>
        let currentAttachments = [];
        const filePreview = document.getElementById('file-preview');
        
        function showResult(container, type, message) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        // Test clipboard paste
        document.addEventListener('paste', async (e) => {
            const pasteResult = document.getElementById('paste-result');
            pasteResult.innerHTML = '';
            
            const clipboardData = e.clipboardData || window.clipboardData;
            if (!clipboardData) {
                showResult(pasteResult, 'error', '❌ No clipboard data available');
                return;
            }
            
            const items = Array.from(clipboardData.items);
            let hasImage = false;
            
            for (const item of items) {
                if (item.type.startsWith('image/')) {
                    hasImage = true;
                    e.preventDefault();
                    
                    const file = item.getAsFile();
                    if (file) {
                        showResult(pasteResult, 'success', `✅ Image pasted: ${file.name || 'clipboard-image'} (${(file.size/1024).toFixed(1)}KB)`);
                        
                        try {
                            await processClipboardImage(file);
                            showResult(pasteResult, 'success', '✅ Image preview created successfully');
                        } catch (error) {
                            showResult(pasteResult, 'error', `❌ Preview failed: ${error.message}`);
                        }
                    }
                    break;
                }
            }
            
            if (!hasImage) {
                showResult(pasteResult, 'info', 'ℹ️ No image found in clipboard');
            }
        });
        
        // Test file upload
        document.getElementById('file-input').addEventListener('change', async (e) => {
            const files = Array.from(e.target.files);
            const uploadResult = document.getElementById('upload-result');
            uploadResult.innerHTML = '';
            
            for (const file of files) {
                showResult(uploadResult, 'success', `✅ File selected: ${file.name} (${(file.size/1024).toFixed(1)}KB)`);
                
                try {
                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: await fileToBase64(file),
                        isImage: true
                    };
                    
                    currentAttachments.push(fileData);
                    showFilePreview(fileData);
                    filePreview.style.display = 'block';
                    
                    showResult(uploadResult, 'success', '✅ Image preview created successfully');
                } catch (error) {
                    showResult(uploadResult, 'error', `❌ Preview failed: ${error.message}`);
                }
            }
        });
        
        // Process clipboard image
        async function processClipboardImage(file) {
            const fileData = {
                name: `clipboard-image-${Date.now()}.png`,
                type: file.type,
                size: file.size,
                content: await fileToBase64(file),
                isImage: true,
                isClipboard: true
            };
            
            currentAttachments.push(fileData);
            showFilePreview(fileData);
            filePreview.style.display = 'block';
        }
        
        // Convert file to base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }
        
        // Show file preview (copied from main script.js)
        function showFilePreview(file) {
            const previewItem = document.createElement('div');
            previewItem.classList.add('preview-item');

            // Mark clipboard images
            if (file.isClipboard) {
                previewItem.setAttribute('data-clipboard', 'true');
            }

            // Create container for filename and buttons
            const header = document.createElement('div');
            header.style.display = 'flex';
            header.style.alignItems = 'center';
            header.style.justifyContent = 'space-between';

            // File name with icon/thumbnail
            const fileNameContainer = document.createElement('div');
            fileNameContainer.style.display = 'flex';
            fileNameContainer.style.alignItems = 'center';
            fileNameContainer.style.gap = '8px';
            fileNameContainer.style.flexGrow = '1';
            fileNameContainer.style.overflow = 'hidden';

            // Add thumbnail for images
            if (file.isImage) {
                const thumbnail = document.createElement('img');
                thumbnail.src = file.content;
                thumbnail.alt = file.name;
                thumbnail.style.width = '32px';
                thumbnail.style.height = '32px';
                thumbnail.style.objectFit = 'cover';
                thumbnail.style.borderRadius = '4px';
                thumbnail.style.border = '1px solid #ddd';
                thumbnail.style.flexShrink = '0';
                fileNameContainer.appendChild(thumbnail);
            }

            // File name
            const fileName = document.createElement('span');
            fileName.textContent = file.name;
            fileName.style.overflow = 'hidden';
            fileName.style.textOverflow = 'ellipsis';
            fileName.style.whiteSpace = 'nowrap';
            fileNameContainer.appendChild(fileName);

            // View button
            const viewBtn = document.createElement('button');
            viewBtn.classList.add('view-preview');
            viewBtn.innerHTML = file.isImage ? '🖼️' : '👁️';
            viewBtn.title = file.isImage ? 'Toggle image preview' : 'View file content';
            viewBtn.addEventListener('click', () => {
                const contentDiv = previewItem.querySelector('.file-content');
                if (contentDiv) {
                    const isVisible = contentDiv.style.display !== 'none';
                    contentDiv.style.display = isVisible ? 'none' : 'block';
                }
            });

            // Remove button
            const removeBtn = document.createElement('button');
            removeBtn.classList.add('remove-preview');
            removeBtn.innerHTML = '🗑️';
            removeBtn.title = 'Remove file';
            removeBtn.addEventListener('click', () => {
                currentAttachments = currentAttachments.filter(f => f !== file);
                previewItem.remove();
                if (currentAttachments.length === 0) {
                    filePreview.style.display = 'none';
                }
            });

            header.appendChild(fileNameContainer);
            header.appendChild(viewBtn);
            header.appendChild(removeBtn);
            previewItem.appendChild(header);

            // Content area (visible by default for images)
            const contentDiv = document.createElement('div');
            contentDiv.classList.add('file-content');
            contentDiv.style.display = file.isImage ? 'block' : 'none';
            contentDiv.style.marginTop = '8px';
            contentDiv.style.whiteSpace = 'pre-wrap';
            contentDiv.style.maxHeight = '200px';
            contentDiv.style.overflow = 'auto';
            contentDiv.style.padding = '8px';
            contentDiv.style.backgroundColor = '#f5f5f5';
            contentDiv.style.borderRadius = '4px';

            if (file.isImage) {
                // For images, show the image preview
                const imgPreview = document.createElement('img');
                imgPreview.src = file.content;
                imgPreview.alt = file.name;
                imgPreview.style.maxWidth = '100%';
                imgPreview.style.maxHeight = '180px';
                imgPreview.style.display = 'block';
                imgPreview.style.margin = '0 auto';
                contentDiv.appendChild(imgPreview);
            }

            previewItem.appendChild(contentDiv);
            filePreview.appendChild(previewItem);
        }
        
        // Make paste area focusable
        const pasteArea = document.getElementById('paste-area');
        pasteArea.tabIndex = 0;
        pasteArea.addEventListener('click', () => pasteArea.focus());
    </script>
</body>
</html>
