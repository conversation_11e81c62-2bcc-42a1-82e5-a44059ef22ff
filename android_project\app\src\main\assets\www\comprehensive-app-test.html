<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive App Function Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .warning {
            background-color: #ffc107;
            color: black;
        }
        .info {
            background-color: #17a2b8;
            color: white;
        }
        #test-results {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 10px;
            background-color: #1e1e1e;
        }
        .function-status {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-pass {
            background-color: #28a745;
        }
        .status-fail {
            background-color: #dc3545;
        }
        .status-unknown {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>Comprehensive App Function Test Suite</h1>
    <p>This test suite verifies that all app functions work correctly after the chat history system rewrite.</p>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        <button class="test-button danger" onclick="resetApp()">Reset App Data</button>
    </div>

    <div class="test-section">
        <h2>Core Function Tests</h2>
        <button class="test-button" onclick="testChatSystem()">Test Chat System</button>
        <button class="test-button" onclick="testAIProviders()">Test AI Providers</button>
        <button class="test-button" onclick="testMessageHandling()">Test Message Handling</button>
        <button class="test-button" onclick="testFileAttachments()">Test File Attachments</button>
    </div>

    <div class="test-section">
        <h2>UI Function Tests</h2>
        <button class="test-button" onclick="testThemeSystem()">Test Theme System</button>
        <button class="test-button" onclick="testCodeEditor()">Test Code Editor</button>
        <button class="test-button" onclick="testMobileUI()">Test Mobile UI</button>
        <button class="test-button" onclick="testExportImport()">Test Export/Import</button>
    </div>

    <div class="test-section">
        <h2>Function Status Overview</h2>
        <div id="function-status"></div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // Test utilities
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`TEST: ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function resetApp() {
            if (confirm('This will clear all app data including chat history. Continue?')) {
                localStorage.clear();
                logResult('App data reset completed', 'warning');
                updateFunctionStatus();
            }
        }

        // Function status tracking
        const functionStatus = {
            chatSystem: 'unknown',
            aiProviders: 'unknown',
            messageHandling: 'unknown',
            fileAttachments: 'unknown',
            themeSystem: 'unknown',
            codeEditor: 'unknown',
            mobileUI: 'unknown',
            exportImport: 'unknown'
        };

        function updateFunctionStatus() {
            const statusDiv = document.getElementById('function-status');
            statusDiv.innerHTML = '';
            
            for (const [func, status] of Object.entries(functionStatus)) {
                const statusItem = document.createElement('div');
                statusItem.style.cssText = 'margin: 5px 0; display: flex; align-items: center;';
                
                const statusIndicator = document.createElement('span');
                statusIndicator.className = `function-status status-${status}`;
                
                const statusText = document.createElement('span');
                statusText.textContent = `${func}: ${status.toUpperCase()}`;
                
                statusItem.appendChild(statusIndicator);
                statusItem.appendChild(statusText);
                statusDiv.appendChild(statusItem);
            }
        }

        // Test functions
        function testChatSystem() {
            logResult('Testing Chat System...', 'info');
            
            try {
                // Test if new chat system variables exist
                const hasCurrentSession = typeof window.parent.currentChatSession !== 'undefined';
                const hasAllSessions = typeof window.parent.allChatSessions !== 'undefined';
                const hasCreateFunction = typeof window.parent.createNewChatSession === 'function';
                const hasLoadFunction = typeof window.parent.loadChatSession === 'function';
                const hasSaveFunction = typeof window.parent.saveChatSession === 'function';

                if (hasCurrentSession && hasAllSessions && hasCreateFunction && hasLoadFunction && hasSaveFunction) {
                    logResult('✓ Chat system functions are available', 'success');
                    functionStatus.chatSystem = 'pass';
                } else {
                    logResult('✗ Chat system functions are missing', 'error');
                    functionStatus.chatSystem = 'fail';
                }

                // Test storage key
                const newStorageExists = localStorage.getItem('ai_chat_sessions_v2') !== null;
                if (newStorageExists) {
                    logResult('✓ New chat storage system is active', 'success');
                } else {
                    logResult('! New chat storage is empty (expected for new installation)', 'warning');
                }

            } catch (error) {
                logResult(`✗ Chat system test failed: ${error.message}`, 'error');
                functionStatus.chatSystem = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testAIProviders() {
            logResult('Testing AI Providers...', 'info');
            
            try {
                // Test if AI provider manager exists
                const hasProviderManager = typeof window.parent.aiProviderManager !== 'undefined';
                const hasPersonaManager = typeof window.parent.aiPersonaManager !== 'undefined';
                const hasSendMessage = typeof window.parent.sendMessage === 'function';

                if (hasProviderManager && hasPersonaManager && hasSendMessage) {
                    logResult('✓ AI provider system is available', 'success');
                    
                    // Test provider manager methods
                    if (typeof window.parent.aiProviderManager.sendMessage === 'function') {
                        logResult('✓ AI provider sendMessage function exists', 'success');
                        functionStatus.aiProviders = 'pass';
                    } else {
                        logResult('✗ AI provider sendMessage function missing', 'error');
                        functionStatus.aiProviders = 'fail';
                    }
                } else {
                    logResult('✗ AI provider system is missing', 'error');
                    functionStatus.aiProviders = 'fail';
                }

            } catch (error) {
                logResult(`✗ AI providers test failed: ${error.message}`, 'error');
                functionStatus.aiProviders = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testMessageHandling() {
            logResult('Testing Message Handling...', 'info');
            
            try {
                // Test if message functions exist
                const hasAddMessage = typeof window.parent.addMessage === 'function';
                const hasMessageInput = window.parent.document.getElementById('message-input') !== null;
                const hasChatDisplay = window.parent.document.getElementById('chat-display') !== null;

                if (hasAddMessage && hasMessageInput && hasChatDisplay) {
                    logResult('✓ Message handling functions are available', 'success');
                    functionStatus.messageHandling = 'pass';
                } else {
                    logResult('✗ Message handling functions are missing', 'error');
                    functionStatus.messageHandling = 'fail';
                }

            } catch (error) {
                logResult(`✗ Message handling test failed: ${error.message}`, 'error');
                functionStatus.messageHandling = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testFileAttachments() {
            logResult('Testing File Attachments...', 'info');
            
            try {
                // Test if file attachment elements exist
                const hasFileInput = window.parent.document.getElementById('file-input') !== null;
                const hasAttachButton = window.parent.document.getElementById('attach-button') !== null;
                const hasFilePreview = window.parent.document.getElementById('file-preview') !== null;

                if (hasFileInput && hasAttachButton && hasFilePreview) {
                    logResult('✓ File attachment system is available', 'success');
                    functionStatus.fileAttachments = 'pass';
                } else {
                    logResult('✗ File attachment system is missing', 'error');
                    functionStatus.fileAttachments = 'fail';
                }

            } catch (error) {
                logResult(`✗ File attachments test failed: ${error.message}`, 'error');
                functionStatus.fileAttachments = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testThemeSystem() {
            logResult('Testing Theme System...', 'info');
            
            try {
                // Test if theme functions exist
                const hasThemeToggle = window.parent.document.getElementById('theme-toggle') !== null;
                const hasToggleFunction = typeof window.parent.toggleTheme === 'function';

                if (hasThemeToggle && hasToggleFunction) {
                    logResult('✓ Theme system is available', 'success');
                    functionStatus.themeSystem = 'pass';
                } else {
                    logResult('✗ Theme system is missing', 'error');
                    functionStatus.themeSystem = 'fail';
                }

            } catch (error) {
                logResult(`✗ Theme system test failed: ${error.message}`, 'error');
                functionStatus.themeSystem = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testCodeEditor() {
            logResult('Testing Code Editor...', 'info');
            
            try {
                // Test if code editor elements exist
                const hasCodeToggle = window.parent.document.getElementById('code-editor-toggle') !== null;
                const hasCodeContainer = window.parent.document.getElementById('code-editor-container') !== null;

                if (hasCodeToggle && hasCodeContainer) {
                    logResult('✓ Code editor system is available', 'success');
                    functionStatus.codeEditor = 'pass';
                } else {
                    logResult('✗ Code editor system is missing', 'error');
                    functionStatus.codeEditor = 'fail';
                }

            } catch (error) {
                logResult(`✗ Code editor test failed: ${error.message}`, 'error');
                functionStatus.codeEditor = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testMobileUI() {
            logResult('Testing Mobile UI...', 'info');
            
            try {
                // Test if mobile UI elements exist
                const hasMobileToggle = window.parent.document.getElementById('mobile-sidebar-toggle') !== null;
                const hasSidebar = window.parent.document.querySelector('.sidebar') !== null;

                if (hasMobileToggle && hasSidebar) {
                    logResult('✓ Mobile UI system is available', 'success');
                    functionStatus.mobileUI = 'pass';
                } else {
                    logResult('✗ Mobile UI system is missing', 'error');
                    functionStatus.mobileUI = 'fail';
                }

            } catch (error) {
                logResult(`✗ Mobile UI test failed: ${error.message}`, 'error');
                functionStatus.mobileUI = 'fail';
            }
            
            updateFunctionStatus();
        }

        function testExportImport() {
            logResult('Testing Export/Import...', 'info');
            
            try {
                // Test if export/import system exists
                const hasExportManager = typeof window.parent.exportImportManager !== 'undefined';

                if (hasExportManager) {
                    logResult('✓ Export/Import system is available', 'success');
                    functionStatus.exportImport = 'pass';
                } else {
                    logResult('✗ Export/Import system is missing', 'error');
                    functionStatus.exportImport = 'fail';
                }

            } catch (error) {
                logResult(`✗ Export/Import test failed: ${error.message}`, 'error');
                functionStatus.exportImport = 'fail';
            }
            
            updateFunctionStatus();
        }

        function runAllTests() {
            logResult('=== Starting Comprehensive App Function Tests ===', 'info');
            clearResults();
            
            setTimeout(() => testChatSystem(), 100);
            setTimeout(() => testAIProviders(), 200);
            setTimeout(() => testMessageHandling(), 300);
            setTimeout(() => testFileAttachments(), 400);
            setTimeout(() => testThemeSystem(), 500);
            setTimeout(() => testCodeEditor(), 600);
            setTimeout(() => testMobileUI(), 700);
            setTimeout(() => testExportImport(), 800);
            
            setTimeout(() => {
                logResult('=== All Function Tests Completed ===', 'info');
                
                // Summary
                const passCount = Object.values(functionStatus).filter(s => s === 'pass').length;
                const failCount = Object.values(functionStatus).filter(s => s === 'fail').length;
                const totalCount = Object.keys(functionStatus).length;
                
                if (failCount === 0) {
                    logResult(`🎉 All ${totalCount} function groups PASSED!`, 'success');
                } else {
                    logResult(`⚠️ ${passCount}/${totalCount} function groups passed, ${failCount} failed`, 'warning');
                }
            }, 900);
        }

        // Initialize on load
        window.addEventListener('load', () => {
            logResult('Comprehensive App Function Test Suite Loaded', 'info');
            updateFunctionStatus();
        });
    </script>
</body>
</html>
